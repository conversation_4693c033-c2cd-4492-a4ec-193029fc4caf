// Assets/Scripts/Network/NetworkPlayer.cs
using Fusion;
using System;

public class NetworkPlayer : NetworkBehaviour
{
    [Networked] public NetworkString<_16> Nickname { get; set; }

    // OnChanged 속성에 이 값이 변경될 때 호출될 '정적(static)' 메서드의 이름을 지정합니다.
    [Networked(OnChanged = nameof(OnStateChanged))]
    public NetworkBool IsReady { get; set; }

    // UI 스크립트가 플레이어의 상태 변경을 감지할 수 있도록 하는 정적 이벤트입니다.
    public static event Action<NetworkPlayer> OnPlayerUpdated;

    public override void Spawned()
    {
        if (Object.HasInputAuthority)
        {
            RPCSetNickname(NetworkManager.Instance.PlayerNickname);
        }
        gameObject.name = $"Player{Object.Id}";
    }

    public override void Despawned(NetworkRunner runner, bool hasState)
    {
        OnPlayerUpdated?.Invoke(this);
    }

    [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
    private void RPC_SetNickname(string nickname)
    {
        this.Nickname = nickname;
    }

    [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
    public void RPC_SetReady(NetworkBool isReady)
    {
        IsReady = isReady;
    }

    // OnChanged 콜백은 반드시 'public static' 이어야 합니다.
    // 이 메서드는 IsReady 또는 Nickname 값이 네트워크를 통해 변경될 때 모든 클라이언트에서 호출됩니다.
    public static void OnStateChanged(Changed<NetworkPlayer> changed)
    {
        // 'changed.Behaviour'는 값이 변경된 NetworkPlayer 컴포넌트의 인스턴스를 가리킵니다.
        // 이 인스턴스를 이벤트에 담아 보내면, UI 스크립트는 어떤 플레이어의 정보가 업데이트되었는지 알 수 있습니다.
        if (OnPlayerUpdated != null)
        {
            OnPlayerUpdated.Invoke(changed.Behaviour);
        }
    }
}