// Assets/Scripts/Game/Skills/Effects/AreaDamageEffect.cs
using UnityEngine;
using System.Collections.Generic;
// using Fusion;

[CreateAssetMenu(fileName = "New AreaDamageEffect", menuName = "Game/Skills/Effects/Area Damage")]
public class AreaDamageEffect : SkillEffect
{
    [Header("데미지 설정")]
    public float damageAmount;
    public DamageType damageType;

    // public override void ApplyEffect(NetworkRunner runner, GameObject caster, List<GameObject> targets)
    public override void ApplyEffect(MonoBehaviour runner, GameObject caster, List<GameObject> targets) // runner 타입을 MonoBehaviour로 변경
    {
        // if (runner != null && !runner.IsServer) return; // 네트워크 모드에서는 이 라인이 필요합니다.

        foreach (var target in targets)
        {
            if (target != null && target.TryGetComponent<IEnemy>(out var enemy))
            {
                enemy.TakeDamage(damageAmount, damageType);
            }
        }
    }
}