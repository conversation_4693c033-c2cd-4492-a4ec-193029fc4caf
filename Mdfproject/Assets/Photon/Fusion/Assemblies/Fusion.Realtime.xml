<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Fusion.Realtime</name>
    </assembly>
    <members>
        <member name="T:Fusion.RegionInfo">
            <summary>
            Wrapper for realtime region information.
            </summary>
        </member>
        <member name="F:Fusion.RegionInfo.RegionCode">
            <summary>
            The code of the region.
            </summary>
        </member>
        <member name="F:Fusion.RegionInfo.RegionPing">
            <summary>
            The ping of the region.
            </summary>
        </member>
        <member name="T:Fusion.FusionRealtimeProxy">
            <summary>
            Class to provide access to some realtime operations and API.
            </summary>
        </member>
        <member name="F:Fusion.FusionRealtimeProxy.REGION_INFO_CACHE_TIME">
            <summary>
            Time the cache will be available in seconds since the last request.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.FusionRelayClient">
            <summary>
            Responsible for running a thread that will keep the LoadBalancingClient connected to the Cloud
            when the application is in the background
            </summary>
            <summary>
            Fusion Realtime Client
            
            This will deal with all communication done with the Photon Cloud
            </summary>
            <summary>
            Fusion Realtime Client
            
            This will deal with all communication done with the Photon Cloud
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FusionRelayClient._connectionHandler">
            <summary>
            Reference to the background connection handler
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.StartFallbackSendAck">
            <summary>
            Starts a new thread to send ACK to Cloud
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.StopFallbackSendAck">
            <summary>
            Stops the currently running background thread.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.OnEventHandler(ExitGames.Client.Photon.EventData)">
            <summary>
            Handles all received events from the Photon Cloud
            </summary>
            <param name="evt">Event Data</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.SendEvent(System.Int32,System.Byte,System.Byte*,System.Int32,System.Boolean)">
            <summary>
            Send data to another Actor on the Room
            </summary>
            <param name="target">Target Actor of the Event</param>
            <param name="eventCode">Event Code</param>
            <param name="buffer">Data to be sent</param>
            <param name="bufferLength">Buffer Length</param>
            <param name="reliable">Flag to set reliability on the Event</param>
            <returns>True if the event was sent, false otherwise</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.ExtractData(System.Object,System.Byte[],System.Int32@)">
            <summary>
            Utility method used to extract the content of a <see cref="T:ExitGames.Client.Photon.ByteArraySlice"/> from a object holder.
            This is necessary so there are no references of Photon Lib DLLs other than on the Fusion.Realtime project
            </summary>
            <param name="dataObj">Data Object Holder, this must be a reference of a <see cref="T:ExitGames.Client.Photon.ByteArraySlice"/></param>
            <param name="buffer">Buffer to write the content of the array slice</param>
            <param name="bufferLength">Output size of the written buffer</param>
            <returns>True if the extraction was done correctly, false otherwise</returns>
        </member>
        <member name="F:Fusion.Photon.Realtime.FusionRelayClient.FUSION_PLUGIN_NAME">
            <summary>
            Fusion Plugin Name for request
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FusionRelayClient.SERVER_HOST_CN">
            <summary>
            Alternative Name Server for CN Region
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.FusionRelayClient.IsReadyAndInRoom">
            <summary>
            Flag to signal if the Client is Ready to perform cloud action and it is in a Room
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.FusionRelayClient.IsEncryptionEnabled">
            <summary>
            Flag to signal if the Photon Cloud connection is encrypted by default
            </summary>
        </member>
        <member name="E:Fusion.Photon.Realtime.FusionRelayClient.OnRoomChanged">
            <summary>
            Signal if some room property has changed
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.FusionRelayClient.UseDefaultPorts">
            <summary>
            Set if the Client should use the Default or Alternative Photon Cloud Ports
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.FusionRelayClient.DisconnectTimeout">
            <summary>
            <inheritdoc cref="P:ExitGames.Client.Photon.PhotonPeer.DisconnectTimeout"/>
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.LoadPhotonEncryptorType">
            <summary>
            Try to load the first implementation of <see cref="T:ExitGames.Client.Photon.Encryption.IPhotonEncryptor"/> found in the current AppDomain
            </summary>
            <returns>Type of the first implementation of <see cref="T:ExitGames.Client.Photon.Encryption.IPhotonEncryptor"/> found</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.UpdateRoomProperties(System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty})">
            <summary>
            Change the Custom Properties of the current Room
            </summary>
            <param name="customProperties">New set of Custom Properties</param>
            <returns>True if the change was made, false otherwise</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.UpdateRoomIsVisible(System.Boolean)">
            <summary>
            Change the IsVisible Property of the current Room
            </summary>
            <param name="value">New value of IsVisible</param>
            <returns>True if the change was made, false otherwise</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.UpdateRoomIsOpen(System.Boolean)">
            <summary>
            Change the IsOpen Property of the current Room
            </summary>
            <param name="value">New value of IsOpen</param>
            <returns>True if the change was made, false otherwise</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.Update">
            <summary>
            Used to keep the client communication
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.BuildEnterRoomParams(Fusion.Photon.Realtime.TypedLobby,System.String,System.Int32,System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty},System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Build a new EnterRoomParams ref using the default configs and optional Room Name
            </summary>
            <param name="typedLobby">Which lobby the Room should exits</param>
            <param name="roomName">Room Name, if not set, a Random Name will be used</param>
            <param name="maxPlayers">Set the max number of players per Session</param>
            <param name="customProperties">Optional Room Custom Properties</param>
            <param name="isOpen">Signal if the Session should be Open/Closed</param>
            <param name="isVisible">Signal if the Session should Visible/Invisible</param>
            <param name="useDefaultEmptyRoomTtl">Signal fi the Default EmptyRoomTTL (0) should be used or a custom one</param>
            <param name="extendedTtl">Signal if the Session should have an extended Time To Live</param>
            <returns>EnterRoomParams reference.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.BuildJoinParams(Fusion.Photon.Realtime.TypedLobby,System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty},Fusion.Photon.Realtime.MatchmakingMode)">
            <summary>
            Build a new OpJoinRandomRoomParams that will be used to setup which Room the local peer wants to join
            </summary>
            <param name="typedLobby">Type of Lobby to search rooms</param>
            <param name="customProperties">Optional list of filter parameters</param>
            <param name="matchmakingMode">Optional Matchmaking Mode</param>
            <returns>OpJoinRandomRoomParams reference</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionRelayClient.BuildSessionCustomPropertyHolders(System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty},ExitGames.Client.Photon.Hashtable@,System.String[]@)">
            <summary>
            Convert a <see cref="T:System.Collections.Generic.Dictionary`2"/> into a pair of <see cref="T:ExitGames.Client.Photon.Hashtable"/> and <see cref="T:System.String"/> respectively 
            representing the custom properties of a session and the property names that will be published on the Lobby
            </summary>
            <param name="customProperties">Dictionary to be converted</param>
            <param name="sessionCustomProperties">Hashtable with all allowed Custom Properties</param>
            <param name="publicSessionProperties">String array with all public key names</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.GetRegionsAsync(Fusion.Photon.Realtime.LoadBalancingClient,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Asynchronously retrieves the list of regions from the NameServer.
            </summary>
            <param name="client">The LoadBalancingClient instance.</param>
            <param name="throwOnError">Indicates whether to throw an exception on error. Defaults to true.</param>
            <param name="createServiceTask">Indicates whether to run the client's service during the operation. Defaults to true.</param>
            <param name="externalCancelationToken">An optional cancellation token that can be used to cancel the operation. Defaults to an empty cancellation token.</param>
            <returns>A task that represents the asynchronous operation. The task result the region handler after pinging the regions.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.ConnectUsingSettingsAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.AppSettings,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Connect to master server.
            </summary>
            <param name="client">Client</param>
            <param name="appSettings">App settings</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When connected to master server callback was called.</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.AuthenticationFailedException">Is thrown when the authentication failed</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.ReconnectAndRejoinAsync(Fusion.Photon.Realtime.LoadBalancingClient,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Asynchronously reconnects and rejoins a room.
            </summary>
            <param name="client">The client that is reconnecting and rejoining.</param>
            <param name="throwOnError">Indicates whether to throw an exception on error. Defaults to true.</param>
            <param name="createServiceTask">Indicates whether to run the client's service during the operation. Defaults to true.</param>
            <param name="externalCancellationToken">An optional cancellation token that can be used to cancel the operation. Defaults to an empty cancellation token.</param>
            <returns>A task that represents the asynchronous operation. The task result is a boolean indicating whether the operation was successful.</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Thrown when the client is still connected or when the reconnecting operation fails to start.</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Thrown when the connection is terminated during the operation.</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Thrown when the operation completes unsuccessfully.</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Thrown when the operation times out.</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.DisconnectAsync(Fusion.Photon.Realtime.LoadBalancingClient,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Disconnects the client.
            </summary>
            <param name="client">Client.</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>Returns when the client has successfully disconnected</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.CreateRoomAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.EnterRoomParams,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Create and join a room.
            </summary>
            <param name="client">Client object</param>
            <param name="enterRoomParams">Enter room params</param>
            <param name="throwOnError">Set ErrorCode as result on RoomCreateFailed or RoomJoinFailed</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When the room has been entered</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.CreateOrJoinRoomAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.EnterRoomParams,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Create or Join a Room.
            </summary>
            <param name="client">Client object</param>
            <param name="enterRoomParams">Enter room params</param>
            <param name="throwOnError">Set ErrorCode as result on RoomCreateFailed or RoomJoinFailed</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When the room has been entered</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.JoinRoomAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.EnterRoomParams,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Join room.
            </summary>
            <param name="client">Client object</param>
            <param name="enterRoomParams">Enter room params</param>
            <param name="throwOnError">Set ErrorCode as result when JoinRoomFailed</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When room has been entered</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.JoinRandomOrCreateRoomAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.OpJoinRandomRoomParams,Fusion.Photon.Realtime.EnterRoomParams,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Join random or Create room
            </summary>
            <param name="client">Client object</param>
            <param name="joinRandomRoomParams">Join random room params</param>
            <param name="enterRoomParams">Enter room params</param>
            <param name="throwOnError">Set ErrorCode as result when operation fails with ErrorCode</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When inside a room</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.JoinRandomRoomAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.OpJoinRandomRoomParams,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Join a Random Room
            </summary>
            <param name="client">Client object</param>
            <param name="joinRandomRoomParams">Join random room params</param>
            <param name="throwOnError">Set ErrorCode as result when operation fails with ErrorCode</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>When inside a room</returns>
            <exception cref="T:Fusion.Photon.Realtime.Async.DisconnectException">Is thrown when the connection terminated</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationStartException">Is thrown when the operation could not be started</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationException">Is thrown when the operation completed unsuccessfully</exception>
            <exception cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException">Is thrown when the operation timed out</exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.JoinLobbyAsync(Fusion.Photon.Realtime.LoadBalancingClient,Fusion.Photon.Realtime.TypedLobby,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Join a Lobby
            </summary>
            <param name="client">Client object</param>
            <param name="lobby">Lobby to join</param>
            <param name="throwOnError">Set ErrorCode as result when operation fails with ErrorCode</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancelationToken">Optional external cancellation token</param>
            <returns>When inside a Lobby</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.CreateOpHandler(Fusion.Photon.Realtime.LoadBalancingClient,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Create a <see cref="T:Fusion.Photon.Realtime.Async.OperationHandler"/> instance, sets up the Photon callbacks, schedules removing them, create a connection service task.
            The handler will monitor the Photon callbacks and complete, fault accordingly. 
            Use the callbacks <see cref="M:Fusion.Photon.Realtime.Async.OperationHandler.OnCreatedRoom"/> to change the default handling.
            <see cref="P:Fusion.Photon.Realtime.Async.OperationHandler.Task"/> can complete with ErrorCode.Ok, exception on errors and a timeout <see cref="T:Fusion.Photon.Realtime.Async.OperationTimeoutException"/>.
            </summary>
            <param name="client">Client</param>
            <param name="throwOnErrors">The default implementation will throw an exception on every unexpected result, set this to false to return a result <see cref="T:Fusion.Photon.Realtime.ErrorCode"/> instead</param>
            <param name="createServiceTask">Runs client.Service() during the operation</param>
            <param name="externalCancellationToken">Optional external cancellation token</param>
            <returns>Photon Connection Handler object</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Async.LoadBalancingClientAsyncExtensions.Service_ClientUpdate(Fusion.Photon.Realtime.LoadBalancingClient,System.Threading.CancellationToken,System.Threading.Tasks.TaskCompletionSource{System.Int16})">
            <summary>
            Starts a task that calls <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.Service"/> every updateIntervalMs milliseconds.
            The task is stopped by the cancellation token from <see cref="P:Fusion.Photon.Realtime.Async.OperationHandler.Token"/>.
            It will set an exception on the <see cref="T:Fusion.Photon.Realtime.Async.OperationHandler"/> TaskCompletionSource if after the timeout it is still not completed.
            </summary>
            <param name="client">Client</param>
            <param name="token">Cancellation token to stop the update loop</param>
            <param name="completionSource">Completion source is notified on an exception in Service()</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extension.RealtimeExtensions_Hashtable.ConvertToDictionaryProperty(ExitGames.Client.Photon.Hashtable)">
            <summary>
            Convert a <see cref="T:ExitGames.Client.Photon.Hashtable"/> into a <see cref="T:System.Collections.Generic.Dictionary`2"/>
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extension.RealtimeExtensions_Hashtable.ConvertToHashtable(System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty})">
            <summary>
            Convert a <see cref="T:System.Collections.Generic.Dictionary`2"/> into a <see cref="T:ExitGames.Client.Photon.Hashtable"/>
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extension.RealtimeExtensions_Hashtable.CalculateTotalSize(ExitGames.Client.Photon.Hashtable)">
            <summary>
            Calculate the total size a <see cref="T:ExitGames.Client.Photon.Hashtable"/> would take when serialized
            </summary>
            <param name="hashtable">Hashtable to check the size</param>
            <returns>Total size of the Hashtable in serialized format</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extension.RealtimeExtensions_RoomInfo.GetCustomProperties(Fusion.Photon.Realtime.RoomInfo)">
            <summary>
            Convert the Room Custom Properties into a <see cref="T:System.Collections.Generic.Dictionary`2"/>
            </summary>
            <param name="roomInfo">RoomInfo to extract the custom properties</param>
            <returns><see cref="T:System.Collections.Generic.Dictionary`2"/> with the data</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extension.RealtimeExtensions_DictionaryProperties.CalculateTotalSize(System.Collections.Generic.Dictionary{System.String,Fusion.SessionProperty})">
            <summary>
            Calculate the total size a <see cref="T:System.Collections.Generic.Dictionary`2"/> would take when serialized
            </summary>
            <param name="dictionary">Dictionary to check the size</param>
            <returns>Total size of the Dictionary in serialized format</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.AppSettings">
            <summary>
            Settings for Photon application(s) and the server to connect to.
            </summary>
            <remarks>
            This is Serializable for Unity, so it can be included in ScriptableObject instances.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AppIdRealtime">
            <summary>AppId for Realtime or PUN.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AppIdFusion">
            <summary>AppId for Photon Fusion.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AppIdChat">
            <summary>AppId for Photon Chat.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AppIdVoice">
            <summary>AppId for Photon Voice.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AppVersion">
            <summary>The AppVersion can be used to identify builds and will split the AppId distinct "Virtual AppIds" (important for matchmaking).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.UseNameServer">
            <summary>If false, the app will attempt to connect to a Master Server (which is obsolete but sometimes still necessary).</summary>
            <remarks>if true, Server points to a NameServer (or is null, using the default), else it points to a MasterServer.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.FixedRegion">
            <summary>Can be set to any of the Photon Cloud's region names to directly connect to that region.</summary>
            <remarks>if this IsNullOrEmpty() AND UseNameServer == true, use BestRegion. else, use a server</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.BestRegionSummaryFromStorage">
             <summary>Set to a previous BestRegionSummary value before connecting.</summary>
             <remarks>
             This is a value used when the client connects to the "Best Region".<br/>
             If this is null or empty, all regions gets pinged. Providing a previous summary on connect,
             speeds up best region selection and makes the previously selected region "sticky".<br/>
            
             Unity clients should store the BestRegionSummary in the PlayerPrefs.
             You can store the new result by implementing <see cref="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnConnectedToMaster"/>.
             If <see cref="F:Fusion.Photon.Realtime.LoadBalancingClient.SummaryToCache"/> is not null, store this string.
             To avoid storing the value multiple times, you could set SummaryToCache to null.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.Server">
            <summary>The address (hostname or IP) of the server to connect to.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.Port">
            <summary>If not null, this sets the port of the first Photon server to connect to (that will "forward" the client as needed).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.ProxyServer">
            <summary>The address (hostname or IP and port) of the proxy server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.Protocol">
            <summary>The network level protocol to use.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.EnableProtocolFallback">
            <summary>Enables a fallback to another protocol in case a connect to the Name Server fails.</summary>
            <remarks>See: LoadBalancingClient.EnableProtocolFallback.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.AuthMode">
            <summary>Defines how authentication is done. On each system, once or once via a WSS connection (safe).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AppSettings.EnableLobbyStatistics">
            <summary>If true, the client will request the list of currently available lobbies.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.AppSettings.NetworkLogging">
            <summary>Log level for the network lib.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.AppSettings.IsMasterServerAddress">
            <summary>If true, the Server field contains a Master Server address (if any address at all).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.AppSettings.IsBestRegion">
            <summary>If true, the client should fetch the region list from the Name Server and find the one with best ping.</summary>
            <remarks>See "Best Region" in the online docs.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.AppSettings.IsDefaultNameServer">
            <summary>If true, the default nameserver address for the Photon Cloud should be used.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.AppSettings.IsDefaultPort">
            <summary>If true, the default ports for a protocol will be used.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.AppSettings.ToStringFull">
            <summary>ToString but with more details.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.AppSettings.IsAppId(System.String)">
            <summary>Checks if a string is a Guid by attempting to create one.</summary>
            <param name="val">The potential guid to check.</param>
            <returns>True if new Guid(val) did not fail.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.AppSettings.CopyTo(Fusion.Photon.Realtime.AppSettings)">
            <summary>
            Copy the values of the current instance into another instance.
            </summary>
            <param name="d">The destination instance.</param>
            <returns>The destination instance with the copied values.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.AppSettings.GetCopy">
            <summary>
            Get a Copy from the <see cref="T:Fusion.Photon.Realtime.AppSettings"/> into a new instance.
            </summary>
            <returns>Copy of <see cref="T:Fusion.Photon.Realtime.AppSettings"/></returns>
        </member>
        <member name="P:Fusion.Photon.Realtime.ConnectionHandler.Client">
            <summary>
            Photon client to log information and statistics from.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.DisconnectAfterKeepAlive">
             <summary>Option to let the fallback thread call Disconnect after the KeepAliveInBackground time. Default: false.</summary>
             <remarks>
             If set to true, the thread will disconnect the client regularly, should the client not call SendOutgoingCommands / Service.
             This may happen due to an app being in background (and not getting a lot of CPU time) or when loading assets.
            
             If false, a regular timeout time will have to pass (on top) to time out the client.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.KeepAliveInBackground">
            <summary>Defines for how long the Fallback Thread should keep the connection, before it may time out as usual.</summary>
            <remarks>We want to the Client to keep it's connection when an app is in the background (and doesn't call Update / Service Clients should not keep their connection indefinitely in the background, so after some milliseconds, the Fallback Thread should stop keeping it up.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.ConnectionHandler.CountSendAcksOnly">
            <summary>Counts how often the Fallback Thread called SendAcksOnly, which is purely of interest to monitor if the game logic called SendOutgoingCommands as intended.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.ConnectionHandler.FallbackThreadRunning">
            <summary>True if a fallback thread is running. Will call the client's SendAcksOnly() method to keep the connection up.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.ApplyDontDestroyOnLoad">
            <summary>Keeps the ConnectionHandler, even if a new scene gets loaded.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.AppQuits">
            <summary>Indicates that the app is closing. Set in OnApplicationQuit().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.AppPause">
            <summary>Indicates that the (Unity) app is Paused. This means the main thread is not running.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.AppPauseRecent">
            <summary>Indicates that the app was paused within the last 5 seconds.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.AppOutOfFocus">
            <summary>Indicates that the app is not in focus.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ConnectionHandler.AppOutOfFocusRecent">
            <summary>Indicates that the app was out of focus within the last 5 seconds.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.StaticReset">
            <summary>
            Resets statics for Domain Reload
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.Awake">
            <summary></summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.OnDisable">
            <summary>Called by Unity when the application gets closed. Disconnects if OnApplicationQuit() was called before.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.OnApplicationQuit">
            <summary>Called by Unity when the application gets closed. The UnityEngine will also call OnDisable, which disconnects.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.OnApplicationPause(System.Boolean)">
            <summary>Called by Unity when the application gets paused or resumed.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.OnApplicationFocus(System.Boolean)">
            <summary>Called by Unity when the application changes focus.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.IsNetworkReachableUnity">
            <summary>
            When run in Unity, this returns Application.internetReachability != NetworkReachability.NotReachable.
            </summary>
            <returns>Application.internetReachability != NetworkReachability.NotReachable</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.StartFallbackSendAckThread">
            <summary>Starts periodic calls of RealtimeFallbackThread.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.StopFallbackSendAckThread">
            <summary>Stops the periodic calls of RealtimeFallbackThread.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.RealtimeFallbackInvoke">
            <summary>Used in WebGL builds which can't call RealtimeFallback(object state = null) with the state context parameter.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.ConnectionHandler.RealtimeFallback(System.Object)">
            <summary>A thread which runs independently of the Update() calls. Keeps connections online while loading or in background. See <see cref="F:Fusion.Photon.Realtime.ConnectionHandler.KeepAliveInBackground"/>.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.CustomTypesUnity">
            <summary>
            Internally used class, containing de/serialization methods for various Unity-specific classes.
            Adding those to the Photon serialization protocol allows you to send them in events, etc.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.CustomTypesUnity.Register">
            <summary>Register de/serializer methods for Unity specific types. Makes the types usable in RaiseEvent and PUN.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.Extensions">
            <summary>
            This static class defines some useful extension methods for several existing classes (e.g. Vector3, float and others).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.Merge(System.Collections.IDictionary,System.Collections.IDictionary)">
            <summary>
            Merges all keys from addHash into the target. Adds new keys and updates the values of existing keys in target.
            </summary>
            <param name="target">The IDictionary to update.</param>
            <param name="addHash">The IDictionary containing data to merge into target.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.MergeStringKeys(System.Collections.IDictionary,System.Collections.IDictionary)">
            <summary>
            Merges keys of type string to target Hashtable.
            </summary>
            <remarks>
            Does not remove keys from target (so non-string keys CAN be in target if they were before).
            </remarks>
            <param name="target">The target IDictionary passed in plus all string-typed keys from the addHash.</param>
            <param name="addHash">A IDictionary that should be merged partly into target to update it.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.ToStringFull(System.Collections.IDictionary)">
            <summary>Helper method for debugging of IDictionary content, including type-information. Using this is not performant.</summary>
            <remarks>Should only be used for debugging as necessary.</remarks>
            <param name="origin">Some Dictionary or Hashtable.</param>
            <returns>String of the content of the IDictionary.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.ToStringFull``1(System.Collections.Generic.List{``0})">
            <summary>Helper method for debugging of <see cref="T:System.Collections.Generic.List`1"/> content. Using this is not performant.</summary>
            <remarks>Should only be used for debugging as necessary.</remarks>
            <param name="data">Any <see cref="T:System.Collections.Generic.List`1"/> where T implements .ToString().</param>
            <returns>A comma-separated string containing each value's ToString().</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.ToStringFull(System.Object[])">
            <summary>Helper method for debugging of object[] content. Using this is not performant.</summary>
            <remarks>Should only be used for debugging as necessary.</remarks>
            <param name="data">Any object[].</param>
            <returns>A comma-separated string containing each value's ToString().</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.StripToStringKeys(System.Collections.IDictionary)">
            <summary>
            This method copies all string-typed keys of the original into a new Hashtable.
            </summary>
            <remarks>
            Does not recurse (!) into hashes that might be values in the root-hash.
            This does not modify the original.
            </remarks>
            <param name="original">The original IDictonary to get string-typed keys from.</param>
            <returns>New Hashtable containing only string-typed keys of the original.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.StripToStringKeys(ExitGames.Client.Photon.Hashtable)">
            <summary>
            This method copies all string-typed keys of the original into a new Hashtable.
            </summary>
            <remarks>
            Does not recurse (!) into hashes that might be values in the root-hash.
            This does not modify the original.
            </remarks>
            <param name="original">The original IDictonary to get string-typed keys from.</param>
            <returns>New Hashtable containing only string-typed keys of the original.</returns>
        </member>
        <member name="F:Fusion.Photon.Realtime.Extensions.keysWithNullValue">
            <summary>Used by StripKeysWithNullValues.</summary>
            <remarks>
            By making keysWithNullValue a static variable to clear before using, allocations only happen during the warm-up phase
            as the list needs to grow. Once it hit the high water mark for keys you need to remove.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.StripKeysWithNullValues(System.Collections.IDictionary)">
            <summary>Removes all keys with null values.</summary>
            <remarks>
            Photon properties are removed by setting their value to null. Changes the original IDictionary!
            Uses lock(keysWithNullValue), which should be no problem in expected use cases.
            </remarks>
            <param name="original">The IDictionary to strip of keys with null value.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.StripKeysWithNullValues(ExitGames.Client.Photon.Hashtable)">
            <summary>Removes all keys with null values.</summary>
            <remarks>
            Photon properties are removed by setting their value to null. Changes the original IDictionary!
            Uses lock(keysWithNullValue), which should be no problem in expected use cases.
            </remarks>
            <param name="original">The IDictionary to strip of keys with null value.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Extensions.Contains(System.Int32[],System.Int32)">
            <summary>
            Checks if a particular integer value is in an int-array.
            </summary>
            <remarks>This might be useful to look up if a particular actorNumber is in the list of players of a room.</remarks>
            <param name="target">The array of ints to check.</param>
            <param name="nr">The number to lookup in target.</param>
            <returns>True if nr was found in target.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.FriendInfo">
            <summary>
            Used to store info about a friend's online state and in which room he/she is.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ClientState">
            <summary>
            State values for a client, which handles switching Photon server types, some operations, etc.
            </summary>
            \ingroup publicApi
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.PeerCreated">
            <summary>Peer is created but not used yet.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Authenticating">
            <summary>Transition state while connecting to a server. On the Photon Cloud this sends the AppId and AuthenticationValues (UserID).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Authenticated">
            <summary>Not Used.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.JoiningLobby">
            <summary>The client sent an OpJoinLobby and if this was done on the Master Server, it will result in. Depending on the lobby, it gets room listings.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.JoinedLobby">
            <summary>The client is in a lobby, connected to the MasterServer. Depending on the lobby, it gets room listings.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.DisconnectingFromMasterServer">
            <summary>Transition from MasterServer to GameServer.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectingToGameServer">
            <summary>Transition to GameServer (client authenticates and joins/creates a room).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectedToGameServer">
            <summary>Connected to GameServer (going to auth and join game).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Joining">
            <summary>Transition state while joining or creating a room on GameServer.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Joined">
            <summary>The client entered a room. The CurrentRoom and Players are known and you can now raise events.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Leaving">
            <summary>Transition state when leaving a room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.DisconnectingFromGameServer">
            <summary>Transition from GameServer to MasterServer (after leaving a room/game).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectingToMasterServer">
            <summary>Connecting to MasterServer (includes sending authentication values).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Disconnecting">
            <summary>The client disconnects (from any server). This leads to state Disconnected.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.Disconnected">
            <summary>The client is no longer connected (to any server). Connect to MasterServer to go on.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectedToMasterServer">
            <summary>Connected to MasterServer. You might use matchmaking or join a lobby now.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectingToNameServer">
            <summary>Client connects to the NameServer. This process includes low level connecting and setting up encryption. When done, state becomes ConnectedToNameServer.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectedToNameServer">
            <summary>Client is connected to the NameServer and established encryption already. You should call OpGetRegions or ConnectToRegionMaster.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.DisconnectingFromNameServer">
            <summary>Clients disconnects (specifically) from the NameServer (usually to connect to the MasterServer).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientState.ConnectWithFallbackProtocol">
            <summary>Client was unable to connect to Name Server and will attempt to connect with an alternative network protocol (TCP).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.JoinType">
            <summary>
            Internal state, how this peer gets into a particular room (joining it or creating it).
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinType.CreateRoom">
            <summary>This client creates a room, gets into it (no need to join) and can set room properties.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinType.JoinRoom">
            <summary>The room existed already and we join into it (not setting room properties).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinType.JoinRandomRoom">
            <summary>Done on Master Server and (if successful) followed by a Join on Game Server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinType.JoinRandomOrCreateRoom">
            <summary>Done on Master Server and (if successful) followed by a Join or Create on Game Server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinType.JoinOrCreateRoom">
            <summary>Client is either joining or creating a room. On Master- and Game-Server.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.DisconnectCause">
            <summary>Enumeration of causes for Disconnects (used in LoadBalancingClient.DisconnectedCause).</summary>
            <remarks>Read the individual descriptions to find out what to do about this type of disconnect.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.None">
            <summary>No error was tracked.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ExceptionOnConnect">
            <summary>OnStatusChanged: The server is not available or the address is wrong. Make sure the port is provided and the server is up.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DnsExceptionOnConnect">
            <summary>OnStatusChanged: Dns resolution for a hostname failed. The exception for this is being caught and logged with error level.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ServerAddressInvalid">
            <summary>OnStatusChanged: The server address was parsed as IPv4 illegally. An illegal address would be e.g. 192.168.1.300. IPAddress.TryParse() will let this pass but our check won't.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.Exception">
            <summary>OnStatusChanged: Some internal exception caused the socket code to fail. This may happen if you attempt to connect locally but the server is not available. In doubt: Contact Exit Games.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.SendException">
            <summary>Send exception.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ReceiveException">
            <summary>Receive exception.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ServerTimeout">
            <summary>OnStatusChanged: The server disconnected this client due to timing out (missing acknowledgement from the client).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ClientTimeout">
            <summary>OnStatusChanged: This client detected that the server's responses are not received in due time.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DisconnectByServerLogic">
            <summary>OnStatusChanged: The server disconnected this client from within the room's logic (the C# code).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DisconnectByServerReasonUnknown">
            <summary>OnStatusChanged: The server disconnected this client for unknown reasons.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.InvalidAuthentication">
            <summary>OnOperationResponse: Authenticate in the Photon Cloud with invalid AppId. Update your subscription or contact Exit Games.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.CustomAuthenticationFailed">
            <summary>OnOperationResponse: Authenticate in the Photon Cloud with invalid client values or custom authentication setup in Cloud Dashboard.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.AuthenticationTicketExpired">
            <summary>The authentication ticket should provide access to any Photon Cloud server without doing another authentication-service call. However, the ticket expired.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.MaxCcuReached">
            <summary>OnOperationResponse: Authenticate (temporarily) failed when using a Photon Cloud subscription without CCU Burst. Update your subscription.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.InvalidRegion">
            <summary>OnOperationResponse: Authenticate when the app's Photon Cloud subscription is locked to some (other) region(s). Update your subscription or master server address.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.OperationNotAllowedInCurrentState">
            <summary>OnOperationResponse: Operation that's (currently) not available for this client (not authorized usually). Only tracked for op Authenticate.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DisconnectByClientLogic">
            <summary>OnStatusChanged: The client disconnected from within the logic (the C# code).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DisconnectByOperationLimit">
            <summary>The client called an operation too frequently and got disconnected due to hitting the OperationLimit. This triggers a client-side disconnect, too.</summary>
            <remarks>To protect the server, some operations have a limit. When an OperationResponse fails with ErrorCode.OperationLimitReached, the client disconnects.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.DisconnectByDisconnectMessage">
            <summary>The client received a "Disconnect Message" from the server. Check the debug logs for details.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.DisconnectCause.ApplicationQuit">
            <summary>Used in case the application quits. Can be useful to not load new scenes or re-connect in OnDisconnected.</summary>
            <remarks>ConnectionHandler.OnDisable() will use this, if the Unity engine already called OnApplicationQuit (ConnectionHandler.AppQuits = true).</remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.ServerConnection">
            <summary>Available server (types) for internally used field: server.</summary>
            <remarks>Photon uses 3 different roles of servers: Name Server, Master Server and Game Server.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ServerConnection.MasterServer">
            <summary>This server is where matchmaking gets done and where clients can get lists of rooms in lobbies.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ServerConnection.GameServer">
            <summary>This server handles a number of rooms to execute and relay the messages between players (in a room).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ServerConnection.NameServer">
            <summary>This server is used initially to get the address (IP) of a Master Server for a specific region. Not used for Photon OnPremise (self hosted).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ClientAppType">
            <summary>Defines which sort of app the LoadBalancingClient is used for: Realtime or Voice.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientAppType.Realtime">
            <summary>Realtime apps are for gaming / interaction. Also used by PUN 2.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientAppType.Voice">
            <summary>Voice apps stream audio.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ClientAppType.Fusion">
            <summary>Fusion clients are for matchmaking and relay in Photon Fusion.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.EncryptionMode">
            <summary>
            Defines how the communication gets encrypted.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EncryptionMode.PayloadEncryption">
            <summary>
            This is the default encryption mode: Messages get encrypted only on demand (when you send operations with the "encrypt" parameter set to true).
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EncryptionMode.DatagramEncryptionGCM">
            <summary>
            Datagram Encryption with GCM.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.PhotonPortDefinition">
            <summary>Container for port definitions.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPortDefinition.NameServerPort">
            <summary>Typical ports: UDP: 5058 or 27000, TCP: 4533, WSS: 19093 or 443.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPortDefinition.MasterServerPort">
            <summary>Typical ports: UDP: 5056 or 27002, TCP: 4530, WSS: 19090 or 443.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPortDefinition.GameServerPort">
            <summary>Typical ports: UDP: 5055 or 27001, TCP: 4531, WSS: 19091 or 443.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.LoadBalancingClient">
            <summary>
            This class implements the Photon LoadBalancing workflow by using a LoadBalancingPeer.
            It keeps a state and will automatically execute transitions between the Master and Game Servers.
            </summary>
            <remarks>
            This class (and the Player class) should be extended to implement your own game logic.
            You can override CreatePlayer as "factory" method for Players and return your own Player instances.
            The State of this class is essential to know when a client is in a lobby (or just on the master)
            and when in a game where the actual gameplay should take place.
            Extension notes:
            An extension of this class should override the methods of the IPhotonPeerListener, as they
            are called when the state changes. Call base.method first, then pick the operation or state you
            want to react to and put it in a switch-case.
            We try to provide demo to each platform where this api can be used, so lookout for those.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.LoadBalancingPeer">
            <summary>
            The client uses a LoadBalancingPeer as API to communicate with the server.
            This is public for ease-of-use: Some methods like OpRaiseEvent are not relevant for the connection state and don't need a override.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.SerializationProtocol">
            <summary>
            Gets or sets the binary protocol version used by this client
            </summary>
            <remarks>
            Use this always instead of setting it via <see cref="P:Fusion.Photon.Realtime.LoadBalancingClient.LoadBalancingPeer"/>
            (<see cref="P:ExitGames.Client.Photon.PhotonPeer.SerializationProtocolType"/>) directly, especially when WSS protocol is used.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.AppVersion">
            <summary>The version of your client. A new version also creates a new "virtual app" to separate players from older client versions.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.AppId">
            <summary>The AppID as assigned from the Photon Cloud. If you host yourself, this is the "regular" Photon Server Application Name (most likely: "LoadBalancing").</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.ClientType">
            <summary>The ClientAppType defines which sort of AppId should be expected. The LoadBalancingClient supports Realtime and Voice app types. Default: Realtime.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.AuthValues">
            <summary>User authentication values to be sent to the Photon server right after connecting.</summary>
            <remarks>Set this property or pass AuthenticationValues by Connect(..., authValues).</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.AuthMode">
            <summary>Enables the new Authentication workflow.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.EncryptionMode">
            <summary>Defines how the communication gets encrypted.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.ExpectedProtocol">
             <summary>Optionally contains a protocol which will be used on Master- and GameServer. </summary>
             <remarks>
             When using AuthMode = AuthModeOption.AuthOnceWss, the client uses a wss-connection on the NameServer but another protocol on the other servers.
             As the NameServer sends an address, which is different per protocol, it needs to know the expected protocol.
            
             This is nullable by design. In many cases, the protocol on the NameServer is not different from the other servers.
             If set, the operation AuthOnce will contain this value and the OpAuth response on the NameServer will execute a protocol switch.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.TokenForInit">
            <summary>Simplifies getting the token for connect/init requests, if this feature is enabled.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.tokenCache">
            <summary>Internally used cache for the server's token. Identifies a user/session and can be used to rejoin.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.IsUsingNameServer">
            <summary>True if this client uses a NameServer to get the Master Server address.</summary>
            <remarks>This value is public, despite being an internal value, which should only be set by this client.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.NameServerHost">
            <summary>Name Server Host Name for Photon Cloud. Without port and without any prefix.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.NameServerAddress">
            <summary>Name Server Address for Photon Cloud (based on current protocol). You can use the default values and usually won't have to set this value.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.ProtocolToNameServerPort">
            <summary>Name Server port per protocol (the UDP port is different than TCP, etc).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.UseAlternativeUdpPorts">
            <summary>Replaced by ServerPortOverrides.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.ServerPortOverrides">
             <summary>Defines overrides for server ports. Used per server-type if > 0. Important: You must change these when the protocol changes!</summary>
             <remarks>
             Typical ports are listed in PhotonPortDefinition.
            
             Instead of using the port provided from the servers, the specified port is used (independent of the protocol).
             If a value is 0 (default), the port is not being replaced.
            
             Different protocols have different typical ports per server-type.
             https://doc.photonengine.com/en-us/pun/current/reference/tcp-and-udp-port-numbers
            
             In case of using the AuthMode AutOnceWss, the name server's protocol is wss, while udp or tcp will be used on the master server and game server.
             Set the ports accordingly per protocol and server.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.EnableProtocolFallback">
             <summary>Enables the fallback to WSS, should the initial connect to the Name Server fail. Some exceptions apply.</summary>
             <remarks>
             For security reasons, a fallback to another protocol is not done when using WSS or AuthMode.AuthOnceWss.
             That would compromise the expected security.
            
             If the fallback is impossible or if that connection also fails, the app logic must handle the case.
             It might even make sense to just try the same connection settings once more (or ask the user to do something about
             the network connectivity, firewalls, etc).
            
             The fallback will use the default Name Server port as defined by ProtocolToNameServerPort.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.CurrentServerAddress">
            <summary>The currently used server address (if any). The type of server is defined by Server property.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.MasterServerAddress">
            <summary>Your Master Server address. In PhotonCloud, call ConnectToRegionMaster() to find your Master Server.</summary>
            <remarks>
            In the Photon Cloud, explicit definition of a Master Server Address is not best practice.
            The Photon Cloud has a "Name Server" which redirects clients to a specific Master Server (per Region and AppId).
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.GameServerAddress">
            <summary>The game server's address for a particular room. In use temporarily, as assigned by master.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.AddressRewriter">
            <summary>Provides a custom function to re-write server addresses in case the client must use a third party relay.</summary>
            <remarks>
            Discord Activities can only communicate with the domain discord.com.
            A forwarding system based on paths is used to replace addresses that are not on that domain.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.Server">
            <summary>The server this client is currently connected or connecting to.</summary>
            <remarks>
            Each server (NameServer, MasterServer, GameServer) allow some operations and reject others.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.ProxyServerAddress">
             <summary>
             Defines a proxy URL for WebSocket connections. Can be the proxy or point to a .pac file.
             </summary>
             <remarks>
             This URL supports various definitions:
            
             "user:pass@proxyaddress:port"<br/>
             "proxyaddress:port"<br/>
             "system:"<br/>
             "pac:"<br/>
             "pac:http://host/path/pacfile.pac"<br/>
            
             Important: Don't define a protocol, except to point to a pac file. the proxy address should not begin with http:// or https://.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.ConnectCount">
            <summary>Count of connections made to any server.</summary>
            <remarks>Statistical value. Increased by OnStatusChanged(StatusCode.Connect).</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.state">
            <summary>Backing field for property.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.State">
            <summary>Current state this client is in. Careful: several states are "transitions" that lead to other states.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.IsConnected">
            <summary>Returns if this client is currently connected or connecting to some type of server.</summary>
            <remarks>This is even true while switching servers. Use IsConnectedAndReady to check only for those states that enable you to send Operations.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.IsConnectedAndReady">
             <summary>
             A refined version of IsConnected which is true only if your connection is ready to send operations.
             </summary>
             <remarks>
             Not all operations can be called on all types of servers. If an operation is unavailable on the currently connected server,
             this will result in a OperationResponse with ErrorCode != 0.
            
             Examples: The NameServer allows OpGetRegions which is not available anywhere else.
             The MasterServer does not allow you to send events (OpRaiseEvent) and on the GameServer you are unable to join a lobby (OpJoinLobby).
            
             To check which server you are on, use: <see cref="P:Fusion.Photon.Realtime.LoadBalancingClient.Server"/>.
             </remarks>
        </member>
        <member name="E:Fusion.Photon.Realtime.LoadBalancingClient.StateChanged">
            <summary>Register a method to be called when this client's ClientState gets set.</summary>
            <remarks>This can be useful to react to being connected, joined into a room, etc.</remarks>
        </member>
        <member name="E:Fusion.Photon.Realtime.LoadBalancingClient.EventReceived">
             <summary>Register a method to be called when an event got dispatched. Gets called after the LoadBalancingClient handled the internal events first.</summary>
             <remarks>
             This is an alternative to extending LoadBalancingClient to override OnEvent().
            
             Note that OnEvent is calling EventReceived after it handled internal events first.
             That means for example: Joining players will already be in the player list but leaving
             players will already be removed from the room.
             </remarks>
        </member>
        <member name="E:Fusion.Photon.Realtime.LoadBalancingClient.OpResponseReceived">
             <summary>Register a method to be called when an operation response is received.</summary>
             <remarks>
             This is an alternative to extending LoadBalancingClient to override OnOperationResponse().
            
             Note that OnOperationResponse gets executed before your Action is called.
             That means for example: The OpJoinLobby response already set the state to "JoinedLobby"
             and the response to OpLeave already triggered the Disconnect before this is called.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.ConnectionCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.MatchMakingCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.InRoomCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.LobbyCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.WebRpcCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.ErrorInfoCallbackTargets">
            <summary>Wraps up the target objects for a group of callbacks, so they can be called conveniently.</summary>
            <remarks>By using Add or Remove, objects can "subscribe" or "unsubscribe" for this group  of callbacks.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.DisconnectedCause">
            <summary>Summarizes (aggregates) the different causes for disconnects of a client.</summary>
            <remarks>
            A disconnect can be caused by: errors in the network connection or some vital operation failing
            (which is considered "high level"). While operations always trigger a call to OnOperationResponse,
            connection related changes are treated in OnStatusChanged.
            The DisconnectCause is set in either case and summarizes the causes for any disconnect in a single
            state value which can be used to display (or debug) the cause for disconnection.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.DisconnectMessage">
            <summary>Defaults to null. Set when the client receives a disconnect info message with a debug string. Reset to null in connect methods.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.TelemetryEnabled">
            <summary>Defines if this client sends telemetry / analytics about how the connection ends.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.telemetrySent">
            <summary>Tells us if "this session" was already reported. We want to send only one report in best case. Re-set on connect.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.SystemConnectionSummary">
            <summary>
            After a to a connection loss or timeout, this summarizes the most relevant system conditions which might have contributed to the loss.
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.InLobby">
            <summary>Internal value if the client is in a lobby.</summary>
            <remarks>This is used to re-set this.State, when joining/creating a room fails.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.CurrentLobby">
            <summary>The lobby this client currently uses. Defined when joining a lobby or creating rooms</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.EnableLobbyStatistics">
             <summary>
             If enabled, the client will get a list of available lobbies from the Master Server.
             </summary>
             <remarks>
             Set this value before the client connects to the Master Server. While connected to the Master
             Server, a change has no effect.
            
             Implement OptionalInfoCallbacks.OnLobbyStatisticsUpdate, to get the list of used lobbies.
            
             The lobby statistics can be useful if your title dynamically uses lobbies, depending (e.g.)
             on current player activity or such.
             In this case, getting a list of available lobbies, their room-count and player-count can
             be useful info.
            
             ConnectUsingSettings sets this to the PhotonServerSettings value.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.lobbyStatistics">
            <summary>Internal lobby stats cache, used by LobbyStatistics.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.LocalPlayer">
            <summary>The local player is never null but not valid unless the client is in a room, too. The ID will be -1 outside of rooms.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.NickName">
            <summary>
            The nickname of the player (synced with others). Same as client.LocalPlayer.NickName.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.UserId">
             <summary>An ID for this user. Sent in OpAuthenticate when you connect. If not set, the PlayerName is applied during connect.</summary>
             <remarks>
             On connect, if the UserId is null or empty, the client will copy the PlayName to UserId. If PlayerName is not set either
             (before connect), the server applies a temporary ID which stays unknown to this client and other clients.
            
             The UserId is what's used in FindFriends and for fetching data for your account (with WebHooks e.g.).
            
             By convention, set this ID before you connect, not while being connected.
             There is no error but the ID won't change while being connected.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.CurrentRoom">
            <summary>The current room this client is connected to (null if none available).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.InRoom">
            <summary>Is true while being in a room (this.state == ClientState.Joined).</summary>
            <remarks>
            Aside from polling this value, game logic should implement IMatchmakingCallbacks in some class
            and react when that gets called.<br/>
            OpRaiseEvent, OpLeave and some other operations can only be used (successfully) when the client is in a room..
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.PlayersOnMasterCount">
            <summary>Statistic value available on master server: Players on master (looking for games).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.PlayersInRoomsCount">
            <summary>Statistic value available on master server: Players in rooms (playing).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.RoomsCount">
            <summary>Statistic value available on master server: Rooms currently created.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.lastJoinType">
            <summary>Internally used to decide if a room must be created or joined on game server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.enterRoomParamsCache">
            <summary>Used when the client arrives on the GS, to join the room with the correct values.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.failedRoomEntryOperation">
            <summary>Used to cache a failed "enter room" operation on the Game Server, to return to the Master Server before calling a fail-callback.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.FriendRequestListMax">
            <summary>Maximum of userIDs that can be sent in one friend list request.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.friendListRequested">
            <summary>Contains the list of names of friends to look up their state on the server.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.IsFetchingFriendList">
            <summary>Internal flag to know if the client currently fetches a friend list.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.CloudRegion">
            <summary>The cloud region this client connects to. Set by ConnectToRegionMaster(). Not set if you don't use a NameServer!</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingClient.CurrentCluster">
            <summary>The cluster name provided by the Name Server.</summary>
            <remarks>
            The value is provided by the OpResponse for OpAuthenticate/OpAuthenticateOnce.
            Default: null. This value only ever updates from the Name Server authenticate response.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.RegionHandler">
            <summary>Contains the list if enabled regions this client may use. Null, unless the client got a response to OpGetRegions.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.bestRegionSummaryFromStorage">
            <summary>Stores the best region summary of a previous session to speed up connecting.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.SummaryToCache">
            <summary>Set when the best region pinging is done.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.connectToBestRegion">
            <summary>Internal connection setting/flag. If the client should connect to the best region or not.</summary>
            <remarks>
            It's set in the Connect...() methods. Only ConnectUsingSettings() sets it to true.
            If true, client will ping available regions and select the best.
            A bestRegionSummaryFromStorage can be used to cut the ping time short.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.LoadBalancingClient.EncryptionDataParameters">
            <summary>Definition of parameters for encryption data (included in Authenticate operation response).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.EncryptionDataParameters.Mode">
            <summary>
            Key for encryption mode
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.EncryptionDataParameters.Secret1">
            <summary>
            Key for first secret
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.EncryptionDataParameters.Secret2">
            <summary>
            Key for second secret
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LoadBalancingClient.CallbackTargetChange.AddTarget">
            <summary>Add if true, remove if false.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.#ctor(ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>Creates a LoadBalancingClient with UDP protocol or the one specified.</summary>
            <param name="protocol">Specifies the network protocol to use for connections.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.#ctor(System.String,System.String,System.String,ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>Creates a LoadBalancingClient, setting various values needed before connecting.</summary>
            <param name="masterAddress">The Master Server's address to connect to. Used in Connect.</param>
            <param name="appId">The AppId of this title. Needed for the Photon Cloud. Find it in the Dashboard.</param>
            <param name="gameVersion">A version for this client/build. In the Photon Cloud, players are separated by AppId, GameVersion and Region.</param>
            <param name="protocol">Specifies the network protocol to use for connections.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.GetNameServerAddress">
            <summary>
            Gets the NameServer Address (with prefix and port), based on the set protocol (this.LoadBalancingPeer.TransportProtocol).
            </summary>
            <returns>NameServer Address (with prefix and port).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ToProtocolAddress(System.String,System.Int32,ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>Build URI from address, use Scheme, Host and Path but set the port as defined by port-field or default port. Calls AddressRewriter if set.</summary>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ConnectUsingSettings(Fusion.Photon.Realtime.AppSettings)">
            <summary>Starts the "process" to connect as defined by the appSettings (AppId, AppVersion, Transport Protocol, Port and more).</summary>
            <remarks>
            A typical connection process wraps up these steps:<br/>
            - Low level connect and init (which establishes a connection that enables operations and responses for the Realtime API).<br/>
            - GetRegions and select best (unless FixedRegion is being used).<br/>
            - Authenticate user for a specific region (this provides a Master Server address to go to and a token).<br/>
            - Disconnect Name Server and connect to Master Server (using the token).<br/>
            - The callback OnConnectedToMaster gets called.<br/>
            <br/>
            Connecting to the servers is a process and this is a non-blocking method.<br/>
            Implement and register the IConnectionCallbacks interface to get callbacks about success or failing connects.<br/>
            <br/>
            Basically all settings for the connection, AppId and servers can be done via the provided parameter.<br/>
            <br/>
            Connecting to the Photon Cloud might fail due to:<br/>
            - Network issues<br/>
            - Region not available<br/>
            - Subscription CCU limit<br/>
            </remarks>
            <see cref="T:Fusion.Photon.Realtime.IConnectionCallbacks"/>
            <see cref="P:Fusion.Photon.Realtime.LoadBalancingClient.AuthValues"/>
            <param name="appSettings">Collection of settings defining this app and how to connect.</param>
            <returns>True if the client can attempt to connect.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ConnectToMasterServer">
             <summary>
             Starts the "process" to connect to a Master Server, using MasterServerAddress and AppId properties.
             </summary>
             <remarks>
             To connect to the Photon Cloud, use ConnectUsingSettings() or ConnectToRegionMaster().
            
             The process to connect includes several steps: the actual connecting, establishing encryption, authentification
             (of app and optionally the user) and connecting to the MasterServer
            
             Users can connect either anonymously or use "Custom Authentication" to verify each individual player's login.
             Custom Authentication in Photon uses external services and communities to verify users. While the client provides a user's info,
             the service setup is done in the Photon Cloud Dashboard.
             The parameter authValues will set this.AuthValues and use them in the connect process.
            
             Connecting to the Photon Cloud might fail due to:
             - Network issues (OnStatusChanged() StatusCode.ExceptionOnConnect)
             - Region not available (OnOperationResponse() for OpAuthenticate with ReturnCode == ErrorCode.InvalidRegion)
             - Subscription CCU limit reached (OnOperationResponse() for OpAuthenticate with ReturnCode == ErrorCode.MaxCcuReached)
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ConnectToNameServer">
            <summary>
            Connects to the NameServer for Photon Cloud, where a region and server list can be obtained.
            </summary>
            <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpGetRegions"/>
            <returns>If the workflow was started or failed right away.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ConnectToRegionMaster(System.String)">
             <summary>
             Connects you to a specific region's Master Server, using the Name Server to get the IP.
             </summary>
             <remarks>
             If the region is null or empty, no connection will be made.
             If the region (code) provided is not available, the connection process will fail on the Name Server.
             This method connects only to the region defined. Any "Best Region" pinging should get done beforehand.
            
             To support "sharding", a region string may contain a "/*" to pick a random cluster or "/clustername"
             to connect to a specific cluster.
             With a "/" or no cluster postfix, the client connects to the default cluster (a specific one
             for a region).
            
             By default, the region string provided by the Name Server does not contain a cluster (and only the
             default cluster is used).
             </remarks>
             <returns>If the operation could be sent. If false, no operation was sent.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.Connect(System.String,System.String,Fusion.Photon.Realtime.ServerConnection)">
            <summary>
            Privately used only for reconnecting.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ReconnectToMaster">
            <summary>Can be used to reconnect to the master server after a disconnect.</summary>
            <remarks>Common use case: Press the Lock Button on a iOS device and you get disconnected immediately.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ReconnectAndRejoin">
            <summary>
            Can be used to return to a room quickly by directly reconnecting to a game server to rejoin a room.
            </summary>
            <remarks>
            Rejoining room will not send any player properties. Instead client will receive up-to-date ones from server.
            If you want to set new player properties, do it once rejoined.
            </remarks>
            <returns>False, if the conditions are not met. Then, this client does not attempt the ReconnectAndRejoin.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.Disconnect">
             <summary>Disconnects the peer from a server or stays disconnected. If the client / peer was connected, a callback will be triggered.</summary>
             <remarks>
             Disconnect will attempt to notify the server of the client closing the connection.
            
             Clients that are in a room, will leave the room. If the room's playerTTL &gt; 0, the player will just become inactive (and may rejoin).
            
             This method will not change the current State, if this client State is PeerCreated, Disconnecting or Disconnected.
             In those cases, there is also no callback for the disconnect. The DisconnectedCause will only change if the client was connected.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.Disconnect(Fusion.Photon.Realtime.DisconnectCause)">
            <summary>Disconnects the client / peer from a server or stays disconnected. Internal method that sets the DisconnectedCause as well.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.DisconnectToReconnect">
            <summary>
            Private Disconnect variant that sets the state, too.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.SimulateConnectionLoss(System.Boolean)">
             <summary>
             Useful to test loss of connection which will end in a client timeout. This modifies LoadBalancingPeer.NetworkSimulationSettings. Read remarks.
             </summary>
             <remarks>
             Use with care as this sets LoadBalancingPeer.IsSimulationEnabled.<br/>
             Read LoadBalancingPeer.IsSimulationEnabled to check if this is on or off, if needed.<br/>
            
             If simulateTimeout is true, LoadBalancingPeer.NetworkSimulationSettings.IncomingLossPercentage and
             LoadBalancingPeer.NetworkSimulationSettings.OutgoingLossPercentage will be set to 100.<br/>
             Obviously, this overrides any network simulation settings done before.<br/>
            
             If you want fine-grained network simulation control, use the NetworkSimulationSettings.<br/>
            
             The timeout will lead to a call to <see cref="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnDisconnected(Fusion.Photon.Realtime.DisconnectCause)"/>, as usual in a client timeout.
            
             You could modify this method (or use NetworkSimulationSettings) to deliberately run into a server timeout by
             just setting the OutgoingLossPercentage = 100 and the IncomingLossPercentage = 0.
             </remarks>
             <param name="simulateTimeout">If true, a connection loss is simulated. If false, the simulation ends.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.Service">
             <summary>
             This method dispatches all available incoming commands and then sends this client's outgoing commands.
             It uses DispatchIncomingCommands and SendOutgoingCommands to do that.
             </summary>
             <remarks>
             The Photon client libraries are designed to fit easily into a game or application. The application
             is in control of the context (thread) in which incoming events and responses are executed and has
             full control of the creation of UDP/TCP packages.
            
             Sending packages and dispatching received messages are two separate tasks. Service combines them
             into one method at the cost of control. It calls DispatchIncomingCommands and SendOutgoingCommands.
            
             Call this method regularly (10..50 times a second).
            
             This will Dispatch ANY received commands (unless a reliable command in-order is still missing) and
             events AND will send queued outgoing commands. Fewer calls might be more effective if a device
             cannot send many packets per second, as multiple operations might be combined into one package.
             </remarks>
             <example>
             You could replace Service by:
            
                 while (DispatchIncomingCommands()); //Dispatch until everything is Dispatched...
                 SendOutgoingCommands(); //Send a UDP/TCP package with outgoing messages
             </example>
             <seealso cref="M:ExitGames.Client.Photon.PhotonPeer.DispatchIncomingCommands"/>
             <seealso cref="M:ExitGames.Client.Photon.PhotonPeer.SendOutgoingCommands"/>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpGetRegions">
            <summary>
            While on the NameServer, this gets you the list of regional servers (short names and their IPs to ping them).
            </summary>
            <returns>If the operation could be sent. If false, no operation was sent (e.g. while not connected to the NameServer).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpFindFriends(System.String[],Fusion.Photon.Realtime.FindFriendsOptions)">
             <summary>
             Request the rooms and online status for a list of friends. All clients should set a unique UserId before connecting. The result is available in this.FriendList.
             </summary>
             <remarks>
             Used on Master Server to find the rooms played by a selected list of users.
             The result will be stored in LoadBalancingClient.FriendList, which is null before the first server response.
            
             Users identify themselves by setting a UserId in the LoadBalancingClient instance.
             This will send the ID in OpAuthenticate during connect (to master and game servers).
             Note: Changing a player's name doesn't make sense when using a friend list.
            
             The list of usernames must be fetched from some other source (not provided by Photon).
            
            
             Internal:<br/>
             The server response includes 2 arrays of info (each index matching a friend from the request):<br/>
             ParameterCode.FindFriendsResponseOnlineList = bool[] of online states<br/>
             ParameterCode.FindFriendsResponseRoomIdList = string[] of room names (empty string if not in a room)<br/>
             <br/>
             The options may be used to define which state a room must match to be returned.
             </remarks>
             <param name="friendsToFind">Array of friend's names (make sure they are unique).</param>
             <param name="options">Options that affect the result of the FindFriends operation.</param>
             <returns>If the operation could be sent (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinLobby(Fusion.Photon.Realtime.TypedLobby)">
            <summary>If already connected to a Master Server, this joins the specified lobby. This request triggers an OnOperationResponse() call and the callback OnJoinedLobby().</summary>
            <param name="lobby">The lobby to join. Use null for default lobby.</param>
            <returns>If the operation could be sent. False, if the client is not IsConnectedAndReady or when it's not connected to a Master Server.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpLeaveLobby">
            <summary>Opposite of joining a lobby. You don't have to explicitly leave a lobby to join another (client can be in one max, at any time).</summary>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinRandomRoom(Fusion.Photon.Realtime.OpJoinRandomRoomParams)">
             <summary>
             Joins a random room that matches the filter. Will callback: OnJoinedRoom or OnJoinRandomFailed.
             </summary>
             <remarks>
             Used for random matchmaking. You can join any room or one with specific properties defined in opJoinRandomRoomParams.
            
             You can use expectedCustomRoomProperties and expectedMaxPlayers as filters for accepting rooms.
             If you set expectedCustomRoomProperties, a room must have the exact same key values set at Custom Properties.
             You need to define which Custom Room Properties will be available for matchmaking when you create a room.
             See: OpCreateRoom(string roomName, RoomOptions roomOptions, TypedLobby lobby)
            
             This operation fails if no rooms are fitting or available (all full, closed or not visible).
             It may also fail when actually joining the room which was found. Rooms may close, become full or empty anytime.
            
             This method can only be called while the client is connected to a Master Server so you should
             implement the callback OnConnectedToMaster.
             Check the return value to make sure the operation will be called on the server.
             Note: There will be no callbacks if this method returned false.
            
            
             This client's State is set to ClientState.Joining immediately, when the operation could
             be called. In the background, the client will switch servers and call various related operations.
            
             When you're in the room, this client's State will become ClientState.Joined.
            
            
             When entering a room, this client's Player Custom Properties will be sent to the room.
             Use LocalPlayer.SetCustomProperties to set them, even while not yet in the room.
             Note that the player properties will be cached locally and are not wiped when leaving a room.
            
             More about matchmaking:
             https://doc.photonengine.com/en-us/realtime/current/reference/matchmaking-and-lobby
            
             You can define an array of expectedUsers, to block player slots in the room for these users.
             The corresponding feature in Photon is called "Slot Reservation" and can be found in the doc pages.
             </remarks>
             <param name="opJoinRandomRoomParams">Optional definition of properties to filter rooms in random matchmaking.</param>
             <returns>If the operation could be sent currently (requires connection to Master Server).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinRandomOrCreateRoom(Fusion.Photon.Realtime.OpJoinRandomRoomParams,Fusion.Photon.Realtime.EnterRoomParams)">
             <summary>
             Attempts to join a room that matches the specified filter and creates a room if none found.
             </summary>
             <remarks>
             This operation is a combination of filter-based random matchmaking with the option to create a new room,
             if no fitting room exists.
             The benefit of that is that the room creation is done by the same operation and the room can be found
             by the very next client, looking for similar rooms.
            
             There are separate parameters for joining and creating a room.
            
             Tickets: Both parameter types have a Ticket value. It is enough to set the opJoinRandomRoomParams.Ticket.
             The createRoomParams.Ticket will not be used.
            
             This method can only be called while connected to a Master Server.
             This client's State is set to ClientState.Joining immediately.
            
             For success, IMatchmakingCallbacks.OnJoinedRoom or IMatchmakingCallbacks.OnCreatedRoom get called.
             In error cases IMatchmakingCallbacks.OnJoinRoomFailed or IMatchmakingCallbacks.OnJoinRandomRoomFailed get called.
            
             More about matchmaking:
             https://doc.photonengine.com/en-us/realtime/current/reference/matchmaking-and-lobby
            
             Check the return value to make sure the operation will be called on the server.
             Note: There will be no callbacks if this method returned false.
             </remarks>
             <returns>If the operation will be sent (requires connection to Master Server).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpCreateRoom(Fusion.Photon.Realtime.EnterRoomParams)">
             <summary>
             Creates a new room. Will callback: OnCreatedRoom and OnJoinedRoom or OnCreateRoomFailed.
             </summary>
             <remarks>
             When successful, the client will enter the specified room and callback both OnCreatedRoom and OnJoinedRoom.
             In all error cases, OnCreateRoomFailed gets called.
            
             Creating a room will fail if the room name is already in use or when the RoomOptions clashing
             with one another. Check the EnterRoomParams reference for the various room creation options.
            
            
             This method can only be called while the client is connected to a Master Server so you should
             implement the callback OnConnectedToMaster.
             Check the return value to make sure the operation will be called on the server.
             Note: There will be no callbacks if this method returned false.
            
            
             When you're in the room, this client's State will become ClientState.Joined.
            
            
             When entering a room, this client's Player Custom Properties will be sent to the room.
             Use LocalPlayer.SetCustomProperties to set them, even while not yet in the room.
             Note that the player properties will be cached locally and are not wiped when leaving a room.
            
             You can define an array of expectedUsers, to block player slots in the room for these users.
             The corresponding feature in Photon is called "Slot Reservation" and can be found in the doc pages.
             </remarks>
             <param name="enterRoomParams">Definition of properties for the room to create.</param>
             <returns>If the operation could be sent currently (requires connection to Master Server).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinOrCreateRoom(Fusion.Photon.Realtime.EnterRoomParams)">
             <summary>
             Joins a specific room by name and creates it on demand. Will callback: OnJoinedRoom or OnJoinRoomFailed.
             </summary>
             <remarks>
             Useful when players make up a room name to meet in:
             All involved clients call the same method and whoever is first, also creates the room.
            
             When successful, the client will enter the specified room.
             The client which creates the room, will callback both OnCreatedRoom and OnJoinedRoom.
             Clients that join an existing room will only callback OnJoinedRoom.
             In all error cases, OnJoinRoomFailed gets called.
            
             Joining a room will fail, if the room is full, closed or when the user
             already is present in the room (checked by userId).
            
             To return to a room, use OpRejoinRoom.
            
             This method can only be called while the client is connected to a Master Server so you should
             implement the callback OnConnectedToMaster.
             Check the return value to make sure the operation will be called on the server.
             Note: There will be no callbacks if this method returned false.
            
             This client's State is set to ClientState.Joining immediately, when the operation could
             be called. In the background, the client will switch servers and call various related operations.
            
             When you're in the room, this client's State will become ClientState.Joined.
            
            
             If you set room properties in roomOptions, they get ignored when the room is existing already.
             This avoids changing the room properties by late joining players.
            
             When entering a room, this client's Player Custom Properties will be sent to the room.
             Use LocalPlayer.SetCustomProperties to set them, even while not yet in the room.
             Note that the player properties will be cached locally and are not wiped when leaving a room.
            
             You can define an array of expectedUsers, to block player slots in the room for these users.
             The corresponding feature in Photon is called "Slot Reservation" and can be found in the doc pages.
             </remarks>
             <param name="enterRoomParams">Definition of properties for the room to create or join.</param>
             <returns>If the operation could be sent currently (requires connection to Master Server).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinRoom(Fusion.Photon.Realtime.EnterRoomParams)">
             <summary>
             Joins a room by name. Will callback: OnJoinedRoom or OnJoinRoomFailed.
             </summary>
             <remarks>
             Useful when using lobbies or when players follow friends or invite each other.
            
             When successful, the client will enter the specified room and callback via OnJoinedRoom.
             In all error cases, OnJoinRoomFailed gets called.
            
             Joining a room will fail if the room is full, closed, not existing or when the user
             already is present in the room (checked by userId).
            
             To return to a room, use OpRejoinRoom.
             When players invite each other and it's unclear who's first to respond, use OpJoinOrCreateRoom instead.
            
             This method can only be called while the client is connected to a Master Server so you should
             implement the callback OnConnectedToMaster.
             Check the return value to make sure the operation will be called on the server.
             Note: There will be no callbacks if this method returned false.
            
             A room's name has to be unique (per region, appid and gameversion).
             When your title uses a global matchmaking or invitations (e.g. an external solution),
             keep regions and the game versions in mind to join a room.
            
            
             This client's State is set to ClientState.Joining immediately, when the operation could
             be called. In the background, the client will switch servers and call various related operations.
            
             When you're in the room, this client's State will become ClientState.Joined.
            
            
             When entering a room, this client's Player Custom Properties will be sent to the room.
             Use LocalPlayer.SetCustomProperties to set them, even while not yet in the room.
             Note that the player properties will be cached locally and are not wiped when leaving a room.
            
             You can define an array of expectedUsers, to reserve player slots in the room for friends or party members.
             The corresponding feature in Photon is called "Slot Reservation" and can be found in the doc pages.
             </remarks>
             <param name="enterRoomParams">Definition of properties for the room to join.</param>
             <returns>If the operation could be sent currently (requires connection to Master Server).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpRejoinRoom(System.String,System.Object)">
             <summary>
             Rejoins a room by roomName (using the userID internally to return).  Will callback: OnJoinedRoom or OnJoinRoomFailed.
             </summary>
             <remarks>
             Used to return to a room, before this user was removed from the players list.
             Internally, the userID will be checked by the server, to make sure this user is in the room (active or inactice).
            
             In contrast to join, this operation never adds a players to a room. It will attempt to retake an existing
             spot in the playerlist or fail. This makes sure the client doesn't accidentally join a room when the
             game logic meant to re-activate an existing actor in an existing room.
            
             This method will fail on the server, when the room does not exist, can't be loaded (persistent rooms) or
             when the userId is not in the player list of this room. This will lead to a callback OnJoinRoomFailed.
            
             Rejoining room will not send any player properties. Instead client will receive up-to-date ones from server.
             If you want to set new player properties, do it once rejoined.
            
             Tickets: If the server requires use of Tickets or if the room was entered with a Ticket initially,
             you will have to provide a ticket as argument.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpLeaveRoom(System.Boolean,System.Boolean)">
             <summary>
             Leaves the current room, optionally telling the server that the user is just becoming inactive. Will callback: OnLeftRoom.
             </summary>
            
             <remarks>
             OpLeaveRoom skips execution when the room is null or the server is not GameServer or the client is disconnecting from GS already.
             OpLeaveRoom returns false in those cases and won't change the state, so check return of this method.
            
             In some cases, this method will skip the OpLeave call and just call Disconnect(),
             which not only leaves the room but also the server. Disconnect also triggers a leave and so that workflow is is quicker.
             </remarks>
             <param name="becomeInactive">If true, this player becomes inactive in the game and can return later (if PlayerTTL of the room is != 0).</param>
             <param name="sendAuthCookie">WebFlag: Securely transmit the encrypted object AuthCookie to the web service in PathLeave webhook when available</param>
             <returns>If the current room could be left (impossible while not in a room).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpGetGameList(Fusion.Photon.Realtime.TypedLobby,System.String)">
             <summary>Gets a list of rooms matching the (non empty) SQL filter for the given SQL-typed lobby.</summary>
             <remarks>
             Operation is only available for lobbies of type SqlLobby and the filter can not be empty.
             It will check those conditions and fail locally, returning false.
            
             This is an async request which triggers a OnOperationResponse() call.
             </remarks>
             <see href="https://doc.photonengine.com/en-us/realtime/current/reference/matchmaking-and-lobby#sql_lobby_type"/>
             <param name="typedLobby">The lobby to query. Has to be of type SqlLobby.</param>
             <param name="sqlLobbyFilter">The sql query statement.</param>
             <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpSetCustomPropertiesOfActor(System.Int32,ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
              <summary>
              Updates and synchronizes a Player's Custom Properties. Optionally, expectedProperties can be provided as condition.
              </summary>
              <remarks>
              Custom Properties are a set of string keys and arbitrary values which is synchronized
              for the players in a Room. They are available when the client enters the room, as
              they are in the response of OpJoin and OpCreate.
            
              Custom Properties either relate to the (current) Room or a Player (in that Room).
            
              Both classes locally cache the current key/values and make them available as
              property: CustomProperties. This is provided only to read them.
              You must use the method SetCustomProperties to set/modify them.
            
              Any client can set any Custom Properties anytime (when in a room).
              It's up to the game logic to organize how they are best used.
            
              You should call SetCustomProperties only with key/values that are new or changed. This reduces
              traffic and performance.
            
              Unless you define some expectedProperties, setting key/values is always permitted.
              In this case, the property-setting client will not receive the new values from the server but
              instead update its local cache in SetCustomProperties.
            
              If you define expectedProperties, the server will skip updates if the server property-cache
              does not contain all expectedProperties with the same values.
              In this case, the property-setting client will get an update from the server and update it's
              cached key/values at about the same time as everyone else.
            
              The benefit of using expectedProperties can be only one client successfully sets a key from
              one known value to another.
              As example: Store who owns an item in a Custom Property "ownedBy". It's 0 initally.
              When multiple players reach the item, they all attempt to change "ownedBy" from 0 to their
              actorNumber. If you use expectedProperties {"ownedBy", 0} as condition, the first player to
              take the item will have it (and the others fail to set the ownership).
            
              Properties get saved with the game state for Turnbased games (which use IsPersistent = true).
              </remarks>
             <param name="actorNr">Defines which player the Custom Properties belong to. ActorID of a player.</param>
             <param name="propertiesToSet">Hashtable of Custom Properties that changes.</param>
             <param name="expectedProperties">Provide some keys/values to use as condition for setting the new values. Client must be in room.</param>
             <param name="webFlags">Defines if the set properties should be forwarded to a WebHook. Client must be in room.</param>
             <returns>
             False if propertiesToSet is null or empty or have zero string keys.
             If not in a room, returns true if local player and expectedProperties and webFlags are null.
             False if actorNr is lower than or equal to zero.
             Otherwise, returns if the operation could be sent to the server.
             </returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpSetPropertiesOfActor(System.Int32,ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
            <summary>Internally used to cache and set properties (including well known properties).</summary>
            <remarks>Requires being in a room (because this attempts to send an operation which will fail otherwise).</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpSetCustomPropertiesOfRoom(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
              <summary>
              Updates and synchronizes this Room's Custom Properties. Optionally, expectedProperties can be provided as condition.
              </summary>
              <remarks>
              Custom Properties are a set of string keys and arbitrary values which is synchronized
              for the players in a Room. They are available when the client enters the room, as
              they are in the response of OpJoin and OpCreate.
            
              Custom Properties either relate to the (current) Room or a Player (in that Room).
            
              Both classes locally cache the current key/values and make them available as
              property: CustomProperties. This is provided only to read them.
              You must use the method SetCustomProperties to set/modify them.
            
              Any client can set any Custom Properties anytime (when in a room).
              It's up to the game logic to organize how they are best used.
            
              You should call SetCustomProperties only with key/values that are new or changed. This reduces
              traffic and performance.
            
              Unless you define some expectedProperties, setting key/values is always permitted.
              In this case, the property-setting client will not receive the new values from the server but
              instead update its local cache in SetCustomProperties.
            
              If you define expectedProperties, the server will skip updates if the server property-cache
              does not contain all expectedProperties with the same values.
              In this case, the property-setting client will get an update from the server and update it's
              cached key/values at about the same time as everyone else.
            
              The benefit of using expectedProperties can be only one client successfully sets a key from
              one known value to another.
              As example: Store who owns an item in a Custom Property "ownedBy". It's 0 initally.
              When multiple players reach the item, they all attempt to change "ownedBy" from 0 to their
              actorNumber. If you use expectedProperties {"ownedBy", 0} as condition, the first player to
              take the item will have it (and the others fail to set the ownership).
            
              Properties get saved with the game state for Turnbased games (which use IsPersistent = true).
              </remarks>
             <param name="propertiesToSet">Hashtable of Custom Properties that changes.</param>
             <param name="expectedProperties">Provide some keys/values to use as condition for setting the new values.</param>
             <param name="webFlags">Defines web flags for an optional PathProperties webhook.</param>
             <returns>
             False if propertiesToSet is null or empty or have zero string keys.
             Otherwise, returns if the operation could be sent to the server.
             </returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpSetPropertiesOfRoom(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
            <summary>Internally used to cache and set properties (including well known properties).</summary>
            <remarks>Requires being in a room (because this attempts to send an operation which will fail otherwise).</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpRaiseEvent(System.Byte,System.Object,Fusion.Photon.Realtime.RaiseEventOptions,ExitGames.Client.Photon.SendOptions)">
            <summary>
            Send an event with custom code/type and any content to the other players in the same room.
            </summary>
            <param name="eventCode">Identifies this type of event (and the content). Your game's event codes can start with 0.</param>
            <param name="customEventContent">Any serializable datatype (including Hashtable like the other OpRaiseEvent overloads).</param>
            <param name="raiseEventOptions">Contains used send options. If you pass null, the default options will be used.</param>
            <param name="sendOptions">Send options for reliable, encryption etc</param>
            <returns>If operation could be enqueued for sending. Sent when calling: Service or SendOutgoingCommands.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpChangeGroups(System.Byte[],System.Byte[])">
             <summary>
             Operation to handle this client's interest groups (for events in room).
             </summary>
             <remarks>
             Note the difference between passing null and byte[0]:
               null won't add/remove any groups.
               byte[0] will add/remove all (existing) groups.
             First, removing groups is executed. This way, you could leave all groups and join only the ones provided.
            
             Changes become active not immediately but when the server executes this operation (approximately RTT/2).
             </remarks>
             <param name="groupsToRemove">Groups to remove from interest. Null will not remove any. A byte[0] will remove all.</param>
             <param name="groupsToAdd">Groups to add to interest. Null will not add any. A byte[0] will add all current.</param>
             <returns>If operation could be enqueued for sending. Sent when calling: Service or SendOutgoingCommands.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ReadoutProperties(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,System.Int32)">
            <summary>
            Privately used to read-out properties coming from the server in events and operation responses (which might be a bit tricky).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ReadoutPropertiesForActorNr(ExitGames.Client.Photon.Hashtable,System.Int32)">
            <summary>
            Privately used only to read properties for a distinct actor (which might be the hashtable OR a key-pair value IN the actorProperties).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.ChangeLocalID(System.Int32,System.Boolean)">
            <summary>Internally used to set the LocalPlayer's actorNumber (from -1 to the actual in-room value) and optionally the userId.</summary>
            <param name="newId">New actorNr assigned when joining a room.</param>
            <param name="applyUserId">Set true for offline mode. If true the player.UserId is set to this.AuthValues.UserId or a new GUID (mimicking online mode).</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.GameEnteredOnGameServer(ExitGames.Client.Photon.OperationResponse)">
            <summary>
            Called internally, when a game was joined or created on the game server successfully.
            </summary>
            <remarks>
            This reads the response, finds out the local player's actorNumber (a.k.a. Player.ID) and applies properties of the room and players.
            Errors for these operations are to be handled before this method is called.
            </remarks>
            <param name="operationResponse">Contains the server's response for an operation called by this peer.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.CreatePlayer(System.String,System.Int32,System.Boolean,ExitGames.Client.Photon.Hashtable)">
            <summary>
            Factory method to create a player instance - override to get your own player-type with custom features.
            </summary>
            <param name="actorName">The name of the player to be created. </param>
            <param name="actorNumber">The player ID (a.k.a. actorNumber) of the player to be created.</param>
            <param name="isLocal">Sets the distinction if the player to be created is your player or if its assigned to someone else.</param>
            <param name="actorProperties">The custom properties for this new player</param>
            <returns>The newly created player</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.CreateRoom(System.String,Fusion.Photon.Realtime.RoomOptions)">
            <summary>Internal "factory" method to create a room-instance.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.DebugReturn(ExitGames.Client.Photon.DebugLevel,System.String)">
            <summary>Debug output of low level api (and this client).</summary>
            <remarks>This method is not responsible to keep up the state of a LoadBalancingClient. Calling base.DebugReturn on overrides is optional.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OnOperationResponse(ExitGames.Client.Photon.OperationResponse)">
             <summary>
             Uses the OperationResponses provided by the server to advance the internal state and call ops as needed.
             </summary>
             <remarks>
             When this method finishes, it will call your OnOpResponseAction (if any). This way, you can get any
             operation response without overriding this class.
            
             To implement a more complex game/app logic, you should implement your own class that inherits the
             LoadBalancingClient. Override this method to use your own operation-responses easily.
            
             This method is essential to update the internal state of a LoadBalancingClient, so overriding methods
             must call base.OnOperationResponse().
             </remarks>
             <param name="operationResponse">Contains the server's response for an operation called by this peer.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OnStatusChanged(ExitGames.Client.Photon.StatusCode)">
            <summary>
            Uses the connection's statusCodes to advance the internal state and call operations as needed.
            </summary>
            <remarks>This method is essential to update the internal state of a LoadBalancingClient. Overriding methods must call base.OnStatusChanged.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OnEvent(ExitGames.Client.Photon.EventData)">
            <summary>
            Uses the photonEvent's provided by the server to advance the internal state and call ops as needed.
            </summary>
            <remarks>This method is essential to update the internal state of a LoadBalancingClient. Overriding methods must call base.OnEvent.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OnMessage(System.Object)">
            <summary>In Photon 4, "raw messages" will get their own callback method in the interface. Not used yet.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OnRegionPingCompleted(Fusion.Photon.Realtime.RegionHandler)">
            <summary>A callback of the RegionHandler, provided in OnRegionListReceived.</summary>
            <param name="regionHandler">The regionHandler wraps up best region and other region relevant info.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.OpWebRpc(System.String,System.Object,System.Boolean)">
             <summary>
             This operation makes Photon call your custom web-service by path/name with the given parameters (converted into Json).
             Use <see cref="M:Fusion.Photon.Realtime.IWebRpcCallback.OnWebRpcResponse(ExitGames.Client.Photon.OperationResponse)"/> as a callback.
             </summary>
             <remarks>
             A WebRPC calls a custom, http-based function on a server you provide. The uriPath is relative to a "base path"
             which is configured server-side. The sent parameters get converted from C# types to Json. Vice versa, the response
             of the web-service will be converted to C# types and sent back as normal operation response.
            
             To use this feature, you have to setup your server:
            
             For a Photon Cloud application, <a href="https://doc.photonengine.com/en-us/realtime/current/reference/webhooks">
             visit the Dashboard </a> and setup "WebHooks". The BaseUrl is used for WebRPCs as well.
            
             The class <see cref="T:Fusion.Photon.Realtime.WebRpcResponse"/> is a helper-class that extracts the most valuable content from the WebRPC
             response.
             </remarks>
             <param name="uriPath">The url path to call, relative to the baseUrl configured on Photon's server-side.</param>
             <param name="parameters">The parameters to send to the web-service method.</param>
             <param name="sendAuthCookie">Defines if the authentication cookie gets sent to a WebHook (if setup).</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)">
             <summary>
             Registers an object for callbacks for the implemented callback-interfaces.
             </summary>
             <remarks>
             Adding and removing callback targets is queued to not mess with callbacks in execution.
             Internally, this means that the addition/removal is done before the LoadBalancingClient
             calls the next callbacks. This detail should not affect a game's workflow.
            
             The covered callback interfaces are: IConnectionCallbacks, IMatchmakingCallbacks,
             ILobbyCallbacks, IInRoomCallbacks, IOnEventCallback and IWebRpcCallback.
            
             See: <a href="https://doc.photonengine.com/en-us/realtime/current/reference/dotnet-callbacks">DotNet Callbacks</a>
             </remarks>
             <param name="target">The object that registers to get callbacks from this client.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)">
             <summary>
             Unregisters an object from callbacks for the implemented callback-interfaces.
             </summary>
             <remarks>
             Adding and removing callback targets is queued to not mess with callbacks in execution.
             Internally, this means that the addition/removal is done before the LoadBalancingClient
             calls the next callbacks. This detail should not affect a game's workflow.
            
             The covered callback interfaces are: IConnectionCallbacks, IMatchmakingCallbacks,
             ILobbyCallbacks, IInRoomCallbacks, IOnEventCallback and IWebRpcCallback.
            
             See: <a href="https://doc.photonengine.com/en-us/realtime/current/reference/dotnet-callbacks"></a>
             </remarks>
             <param name="target">The object that unregisters from getting callbacks.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.UpdateCallbackTargets">
            <summary>
            Applies queued callback cahnges from a queue to the actual containers. Will cause exceptions if used while callbacks execute.
            </summary>
            <remarks>
            There is no explicit check that this is not called during callbacks, however the implemented, private logic takes care of this.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingClient.UpdateCallbackTarget``1(Fusion.Photon.Realtime.LoadBalancingClient.CallbackTargetChange,System.Collections.Generic.List{``0})">
            <summary>Helper method to cast and apply a target per (interface) type.</summary>
            <typeparam name="T">Either of the interfaces for callbacks.</typeparam>
            <param name="change">The queued change to apply (add or remove) some target.</param>
            <param name="container">The container that calls callbacks on it's list of targets.</param>
        </member>
        <member name="T:Fusion.Photon.Realtime.IConnectionCallbacks">
             <summary>
             Collection of "organizational" callbacks for the Realtime Api to cover: Connection and Regions.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnConnected">
             <summary>
             Called to signal that the "low level connection" got established but before the client can call operation on the server.
             </summary>
             <remarks>
             After the (low level transport) connection is established, the client will automatically send
             the Authentication operation, which needs to get a response before the client can call other operations.
            
             Your logic should wait for either: OnRegionListReceived or OnConnectedToMaster.
            
             This callback is useful to detect if the server can be reached at all (technically).
             Most often, it's enough to implement OnDisconnected(DisconnectCause cause) and check for the cause.
            
             This is not called for transitions from the masterserver to game servers.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnConnectedToMaster">
            <summary>
            Called when the client is connected to the Master Server and ready for matchmaking and other tasks.
            </summary>
            <remarks>
            The list of available rooms won't become available unless you join a lobby via LoadBalancingClient.OpJoinLobby.
            You can join rooms and create them even without being in a lobby. The default lobby is used in that case.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnDisconnected(Fusion.Photon.Realtime.DisconnectCause)">
            <summary>
            Called after disconnecting from the Photon server. It could be a failure or an explicit disconnect call
            </summary>
            <remarks>
             The reason for this disconnect is provided as DisconnectCause.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnRegionListReceived(Fusion.Photon.Realtime.RegionHandler)">
             <summary>
             Called when the Name Server provided a list of regions for your title.
             </summary>
             <remarks>
             This callback is called as soon as the list is available. No pings were sent for Best Region selection yet.
             If the client is set to connect to the Best Region (lowest ping), one or more regions get pinged.
             Not all regions are pinged. As soon as the results are final, the client will connect to the best region,
             so you can check the ping results when connected to the Master Server.
            
             Check the RegionHandler class description, to make use of the provided values.
             </remarks>
             <param name="regionHandler">The currently used RegionHandler.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnCustomAuthenticationResponse(System.Collections.Generic.Dictionary{System.String,System.Object})">
             <summary>
             Called when your Custom Authentication service responds with additional data.
             </summary>
             <remarks>
             Custom Authentication services can include some custom data in their response.
             When present, that data is made available in this callback as Dictionary.
             While the keys of your data have to be strings, the values can be either string or a number (in Json).
             You need to make extra sure, that the value type is the one you expect. Numbers become (currently) int64.
            
             Example: void OnCustomAuthenticationResponse(Dictionary&lt;string, object&gt; data) { ... }
             </remarks>
             <see href="https://doc.photonengine.com/en-us/realtime/current/reference/custom-authentication"/>
        </member>
        <member name="M:Fusion.Photon.Realtime.IConnectionCallbacks.OnCustomAuthenticationFailed(System.String)">
             <summary>
             Called when the custom authentication failed. Followed by disconnect!
             </summary>
             <remarks>
             Custom Authentication can fail due to user-input, bad tokens/secrets.
             If authentication is successful, this method is not called. Implement OnJoinedLobby() or OnConnectedToMaster() (as usual).
            
             During development of a game, it might also fail due to wrong configuration on the server side.
             In those cases, logging the debugMessage is very important.
            
             Unless you setup a custom authentication service for your app (in the [Dashboard](https://dashboard.photonengine.com)),
             this won't be called!
             </remarks>
             <param name="debugMessage">Contains a debug message why authentication failed. This has to be fixed during development.</param>
        </member>
        <member name="T:Fusion.Photon.Realtime.ILobbyCallbacks">
             <summary>
             Collection of "organizational" callbacks for the Realtime Api to cover the Lobby.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.ILobbyCallbacks.OnJoinedLobby">
            <summary>
            Called on entering a lobby on the Master Server. The actual room-list updates will call OnRoomListUpdate.
            </summary>
            <remarks>
            While in the lobby, the roomlist is automatically updated in fixed intervals (which you can't modify in the public cloud).
            The room list gets available via OnRoomListUpdate.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.ILobbyCallbacks.OnLeftLobby">
            <summary>
            Called after leaving a lobby.
            </summary>
            <remarks>
            When you leave a lobby, [OpCreateRoom](@ref OpCreateRoom) and [OpJoinRandomRoom](@ref OpJoinRandomRoom)
            automatically refer to the default lobby.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.ILobbyCallbacks.OnRoomListUpdate(System.Collections.Generic.List{Fusion.Photon.Realtime.RoomInfo})">
             <summary>
             Called for any update of the room-listing while in a lobby (InLobby) on the Master Server.
             </summary>
             <remarks>
             Each item is a RoomInfo which might include custom properties (provided you defined those as lobby-listed when creating a room).
             Not all types of lobbies provide a listing of rooms to the client. Some are silent and specialized for server-side matchmaking.
            
             The list is sorted using two criteria: open or closed, full or not. So the list is composed of three groups, in this order:
            
             first group: open and not full (joinable).<br/>
             second group: full but not closed (not joinable).<br/>
             third group: closed (not joinable, could be full or not).<br/>
            
             In each group, entries do not have any particular order (random).
            
             The list of rooms (or rooms' updates) is also limited in number, see Lobby Limits.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.ILobbyCallbacks.OnLobbyStatisticsUpdate(System.Collections.Generic.List{Fusion.Photon.Realtime.TypedLobbyInfo})">
            <summary>
            Called when the Master Server sent an update for the Lobby Statistics.
            </summary>
            <remarks>
            This callback has two preconditions:
            EnableLobbyStatistics must be set to true, before this client connects.
            And the client has to be connected to the Master Server, which is providing the info about lobbies.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.IMatchmakingCallbacks">
             <summary>
             Collection of "organizational" callbacks for the Realtime Api to cover Matchmaking.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnFriendListUpdate(System.Collections.Generic.List{Fusion.Photon.Realtime.FriendInfo})">
             <summary>
             Called when the server sent the response to a FindFriends request.
             </summary>
             <remarks>
             After calling OpFindFriends, the Master Server will cache the friend list and send updates to the friend
             list. The friends includes the name, userId, online state and the room (if any) for each requested user/friend.
            
             Use the friendList to update your UI and store it, if the UI should highlight changes.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnCreatedRoom">
             <summary>
             Called when this client created a room and entered it. OnJoinedRoom() will be called as well.
             </summary>
             <remarks>
             This callback is only called on the client which created a room (see OpCreateRoom).
            
             As any client might close (or drop connection) anytime, there is a chance that the
             creator of a room does not execute OnCreatedRoom.
            
             If you need specific room properties or a "start signal", implement OnMasterClientSwitched()
             and make each new MasterClient check the room's state.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnCreateRoomFailed(System.Int16,System.String)">
             <summary>
             Called when the server couldn't create a room (OpCreateRoom failed).
             </summary>
             <remarks>
             Creating a room may fail for various reasons. Most often, the room already exists (roomname in use) or
             the RoomOptions clash and it's impossible to create the room.
            
             When creating a room fails on a Game Server:
             The client will cache the failure internally and returns to the Master Server before it calls the fail-callback.
             This way, the client is ready to find/create a room at the moment of the callback.
             In this case, the client skips calling OnConnectedToMaster but returning to the Master Server will still call OnConnected.
             Treat callbacks of OnConnected as pure information that the client could connect.
             </remarks>
             <param name="returnCode">Operation ReturnCode from the server.</param>
             <param name="message">Debug message for the error.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnJoinedRoom">
             <summary>
             Called when the LoadBalancingClient entered a room, no matter if this client created it or simply joined.
             </summary>
             <remarks>
             When this is called, you can access the existing players in Room.Players, their custom properties and Room.CustomProperties.
            
             In this callback, you could create player objects. For example in Unity, instantiate a prefab for the player.
            
             If you want a match to be started "actively", enable the user to signal "ready" (using OpRaiseEvent or a Custom Property).
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnJoinRoomFailed(System.Int16,System.String)">
             <summary>
             Called when a previous OpJoinRoom call failed on the server.
             </summary>
             <remarks>
             Joining a room may fail for various reasons. Most often, the room is full or does not exist anymore
             (due to someone else being faster or closing the room).
            
             When joining a room fails on a Game Server:
             The client will cache the failure internally and returns to the Master Server before it calls the fail-callback.
             This way, the client is ready to find/create a room at the moment of the callback.
             In this case, the client skips calling OnConnectedToMaster but returning to the Master Server will still call OnConnected.
             Treat callbacks of OnConnected as pure information that the client could connect.
             </remarks>
             <param name="returnCode">Operation ReturnCode from the server.</param>
             <param name="message">Debug message for the error.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnJoinRandomFailed(System.Int16,System.String)">
             <summary>
             Called when a previous OpJoinRandom (or OpJoinRandomOrCreateRoom etc.) call failed on the server.
             </summary>
             <remarks>
             The most common causes are that a room is full or does not exist (due to someone else being faster or closing the room).
            
             This operation is only ever sent to the Master Server. Once a room is found by the Master Server, the client will
             head off to the designated Game Server and use the operation Join on the Game Server.
            
             When using multiple lobbies (via OpJoinLobby or a TypedLobby parameter), another lobby might have more/fitting rooms.<br/>
             </remarks>
             <param name="returnCode">Operation ReturnCode from the server.</param>
             <param name="message">Debug message for the error.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IMatchmakingCallbacks.OnLeftRoom">
             <summary>
             Called when the local user/client left a room, so the game's logic can clean up it's internal state.
             </summary>
             <remarks>
             When leaving a room, the LoadBalancingClient will disconnect the Game Server and connect to the Master Server.
             This wraps up multiple internal actions.
            
             Wait for the callback OnConnectedToMaster, before you use lobbies and join or create rooms.
            
             OnLeftRoom also gets called, when the application quits.
             It makes sense to check static ConnectionHandler.AppQuits before loading scenes in OnLeftRoom().
             </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.IInRoomCallbacks">
             <summary>
             Collection of "in room" callbacks for the Realtime Api to cover: Players entering or leaving, property updates and Master Client switching.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IInRoomCallbacks.OnPlayerEnteredRoom(Fusion.Photon.Realtime.Player)">
            <summary>
            Called when a remote player entered the room. This Player is already added to the playerlist.
            </summary>
            <remarks>
            If your game starts with a certain number of players, this callback can be useful to check the
            Room.playerCount and find out if you can start.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IInRoomCallbacks.OnPlayerLeftRoom(Fusion.Photon.Realtime.Player)">
             <summary>
             Called when a remote player left the room or became inactive. Check otherPlayer.IsInactive.
             </summary>
             <remarks>
             If another player leaves the room or if the server detects a lost connection, this callback will
             be used to notify your game logic.
            
             Depending on the room's setup, players may become inactive, which means they may return and retake
             their spot in the room. In such cases, the Player stays in the Room.Players dictionary.
            
             If the player is not just inactive, it gets removed from the Room.Players dictionary, before
             the callback is called.
             </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.IInRoomCallbacks.OnRoomPropertiesUpdate(ExitGames.Client.Photon.Hashtable)">
             <summary>
             Called when room properties changed. The propertiesThatChanged contain only the keys that changed.
             </summary>
             <remarks>
             In most cases, this method gets called when some player changes the Room Properties.
             However, there are also "Well Known Properties" (which use byte keys) and this callback may include them.
             Especially when entering a room, the server will also send the required Well Known Properties and they
             are not filtered out for the OnRoomPropertiesUpdate callback.
            
             You can safely ignore the byte typed keys in propertiesThatChanged.
            
             Changing properties is usually done by Room.SetCustomProperties.
             </remarks>
             <param name="propertiesThatChanged"></param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IInRoomCallbacks.OnPlayerPropertiesUpdate(Fusion.Photon.Realtime.Player,ExitGames.Client.Photon.Hashtable)">
            <summary>
            Called when custom player-properties are changed.
            </summary>
            <remarks>
            Changing properties must be done by Player.SetCustomProperties, which causes this callback locally, too.
            </remarks>
            <param name="targetPlayer">Contains Player that changed.</param>
            <param name="changedProps">Contains the properties that changed.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.IInRoomCallbacks.OnMasterClientSwitched(Fusion.Photon.Realtime.Player)">
            <summary>
            Called after switching to a new MasterClient when the current one leaves.
            </summary>
            <remarks>
            This is not called when this client enters a room.
            The former MasterClient is still in the player list when this method get called.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.IOnEventCallback">
             <summary>
             Event callback for the Realtime Api. Covers events from the server and those sent by clients via OpRaiseEvent.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IOnEventCallback.OnEvent(ExitGames.Client.Photon.EventData)">
             <summary>Called for any incoming events.</summary>
             <remarks>
             To receive events, implement IOnEventCallback in any class and register it via AddCallbackTarget
             (either in LoadBalancingClient or PhotonNetwork).
            
             With the EventData.Sender you can look up the Player who sent the event.
            
             It is best practice to assign an eventCode for each different type of content and action, so the Code
             will be essential to read the incoming events.
             </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.IWebRpcCallback">
             <summary>
             Interface for "WebRpc" callbacks for the Realtime Api. Currently includes only responses for Web RPCs.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IWebRpcCallback.OnWebRpcResponse(ExitGames.Client.Photon.OperationResponse)">
             <summary>
             Called when the response to a WebRPC is available. See <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpWebRpc(System.String,System.Object,System.Boolean)"/>.
             </summary>
             <remarks>
             Important: The response.ReturnCode is 0 if Photon was able to reach your web-service.<br/>
             The content of the response is what your web-service sent. You can create a WebRpcResponse from it.<br/>
             Example: WebRpcResponse webResponse = new WebRpcResponse(operationResponse);<br/>
            
             Please note: Class OperationResponse is in a namespace which needs to be "used":<br/>
             using ExitGames.Client.Photon;  // includes OperationResponse (and other classes)
             </remarks>
             <example>
             public void OnWebRpcResponse(OperationResponse response)
             {
                Debug.LogFormat("WebRPC operation response {0}", response.ToStringFull());
                switch (response.ReturnCode)
                {
                    case ErrorCode.Ok:
                        WebRpcResponse webRpcResponse = new WebRpcResponse(response);
                        Debug.LogFormat("Parsed WebRPC response {0}", response.ToStringFull());
                        if (string.IsNullOrEmpty(webRpcResponse.Name))
                        {
                            Debug.LogError("Unexpected: WebRPC response did not contain WebRPC method name");
                        }
                        if (webRpcResponse.ResultCode == 0) // success
                        {
                            switch (webRpcResponse.Name)
                            {
                                // todo: add your code here
                             case GetGameListWebRpcMethodName: // example
                                // ...
                                break;
                         }
                        }
                        else if (webRpcResponse.ResultCode == -1)
                        {
                            Debug.LogErrorFormat("Web server did not return ResultCode for WebRPC method=\"{0}\", Message={1}", webRpcResponse.Name, webRpcResponse.Message);
                        }
                        else
                        {
                            Debug.LogErrorFormat("Web server returned ResultCode={0} for WebRPC method=\"{1}\", Message={2}", webRpcResponse.ResultCode, webRpcResponse.Name, webRpcResponse.Message);
                        }
                        break;
                    case ErrorCode.ExternalHttpCallFailed: // web service unreachable
                        Debug.LogErrorFormat("WebRPC call failed as request could not be sent to the server. {0}", response.DebugMessage);
                        break;
                    case ErrorCode.HttpLimitReached: // too many WebRPCs in a short period of time
                                                     // the debug message should contain the limit exceeded
                       Debug.LogErrorFormat("WebRPCs rate limit exceeded: {0}", response.DebugMessage);
                        break;
                   case ErrorCode.InvalidOperation: // WebRPC not configured at all OR not configured properly OR trying to send on name server
                      if (PhotonNetwork.Server == ServerConnection.NameServer)
                     {
                         Debug.LogErrorFormat("WebRPC not supported on NameServer. {0}", response.DebugMessage);
                     }
                     else
                     {
                         Debug.LogErrorFormat("WebRPC not properly configured or not configured at all. {0}", response.DebugMessage);
                     }
                     break;
                 default:
                     // other unknown error, unexpected
                     Debug.LogErrorFormat("Unexpected error, {0} {1}", response.ReturnCode, response.DebugMessage);
                     break;
              }
             }
            
             </example>
        </member>
        <member name="T:Fusion.Photon.Realtime.IErrorInfoCallback">
             <summary>
             Interface for <see cref="F:Fusion.Photon.Realtime.EventCode.ErrorInfo"/> event callback for the Realtime Api.
             </summary>
             <remarks>
             Classes that implement this interface must be registered to get callbacks for various situations.
            
             To register for callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.AddCallbackTarget(System.Object)"/> and pass the class implementing this interface
             To stop getting callbacks, call <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.RemoveCallbackTarget(System.Object)"/> and pass the class implementing this interface
            
             </remarks>
             \ingroup callbacks
        </member>
        <member name="M:Fusion.Photon.Realtime.IErrorInfoCallback.OnErrorInfo(Fusion.Photon.Realtime.ErrorInfo)">
             <summary>
             Called when the client receives an event from the server indicating that an error happened there.
             </summary>
             <remarks>
             In most cases this could be either:
             1. an error from webhooks plugin (if HasErrorInfo is enabled), read more here:
             https://doc.photonengine.com/en-us/realtime/current/gameplay/web-extensions/webhooks#options
             2. an error sent from a custom server plugin via PluginHost.BroadcastErrorInfoEvent, see example here:
             https://doc.photonengine.com/en-us/server/current/plugins/manual#handling_http_response
             3. an error sent from the server, for example, when the limit of cached events has been exceeded in the room
             (all clients will be disconnected and the room will be closed in this case)
             read more here: https://doc.photonengine.com/en-us/realtime/current/gameplay/cached-events#special_considerations
            
             If you implement <see cref="M:Fusion.Photon.Realtime.IOnEventCallback.OnEvent(ExitGames.Client.Photon.EventData)"/> or <see cref="E:Fusion.Photon.Realtime.LoadBalancingClient.EventReceived"/> you will also get this event.
             </remarks>
             <param name="errorInfo">Object containing information about the error</param>
        </member>
        <member name="T:Fusion.Photon.Realtime.ConnectionCallbacksContainer">
            <summary>
            Container type for callbacks defined by IConnectionCallbacks. See LoadBalancingCallbackTargets.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.MatchMakingCallbacksContainer">
            <summary>
            Container type for callbacks defined by IMatchmakingCallbacks. See MatchMakingCallbackTargets.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.InRoomCallbacksContainer">
            <summary>
            Container type for callbacks defined by IInRoomCallbacks. See InRoomCallbackTargets.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.LobbyCallbacksContainer">
            <summary>
            Container type for callbacks defined by ILobbyCallbacks. See LobbyCallbackTargets.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.WebRpcCallbacksContainer">
            <summary>
            Container type for callbacks defined by IWebRpcCallback. See WebRpcCallbackTargets.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.ErrorInfoCallbacksContainer">
            <summary>
            Container type for callbacks defined by <see cref="T:Fusion.Photon.Realtime.IErrorInfoCallback"/>. See <see cref="F:Fusion.Photon.Realtime.LoadBalancingClient.ErrorInfoCallbackTargets"/>.
            </summary>
            <remarks>
            While the interfaces of callbacks wrap up the methods that will be called,
            the container classes implement a simple way to call a method on all registered objects.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.ErrorInfo">
             <summary>
             Class wrapping the received <see cref="F:Fusion.Photon.Realtime.EventCode.ErrorInfo"/> event.
             </summary>
             <remarks>
             This is passed inside <see cref="M:Fusion.Photon.Realtime.IErrorInfoCallback.OnErrorInfo(Fusion.Photon.Realtime.ErrorInfo)"/> callback.
             If you implement <see cref="M:Fusion.Photon.Realtime.IOnEventCallback.OnEvent(ExitGames.Client.Photon.EventData)"/> or <see cref="E:Fusion.Photon.Realtime.LoadBalancingClient.EventReceived"/> you will also get <see cref="F:Fusion.Photon.Realtime.EventCode.ErrorInfo"/> but not parsed.
            
             In most cases this could be either:
             1. an error from webhooks plugin (if HasErrorInfo is enabled), read more here:
             https://doc.photonengine.com/en-us/realtime/current/gameplay/web-extensions/webhooks#options
             2. an error sent from a custom server plugin via PluginHost.BroadcastErrorInfoEvent, see example here:
             https://doc.photonengine.com/en-us/server/current/plugins/manual#handling_http_response
             3. an error sent from the server, for example, when the limit of cached events has been exceeded in the room
             (all clients will be disconnected and the room will be closed in this case)
             read more here: https://doc.photonengine.com/en-us/realtime/current/gameplay/cached-events#special_considerations
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorInfo.Info">
            <summary>
            String containing information about the error.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.LoadBalancingPeer">
            <summary>
            A LoadBalancingPeer provides the operations and enum definitions needed to use the LoadBalancing server application which is also used in Photon Cloud.
            </summary>
            <remarks>
            This class is internally used.
            The LoadBalancingPeer does not keep a state, instead this is done by a LoadBalancingClient.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.LoadBalancingPeer.PingImplementation">
            <summary>Obsolete accessor to the RegionHandler.PingImplementation.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.#ctor(ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>
            Creates a Peer with specified connection protocol. You need to set the Listener before using the peer.
            </summary>
            <remarks>Each connection protocol has it's own default networking ports for Photon.</remarks>
            <param name="protocolType">The preferred option is UDP.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.#ctor(ExitGames.Client.Photon.IPhotonPeerListener,ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>
            Creates a Peer with specified connection protocol and a Listener for callbacks.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpJoinLobby(Fusion.Photon.Realtime.TypedLobby)">
            <summary>
            Joins the lobby on the Master Server, where you get a list of RoomInfos of currently open rooms.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <param name="lobby">The lobby join to.</param>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpLeaveLobby">
            <summary>
            Leaves the lobby on the Master Server.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <returns>If the operation could be sent (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.RoomOptionsToOpParameters(System.Collections.Generic.Dictionary{System.Byte,System.Object},Fusion.Photon.Realtime.RoomOptions,System.Boolean)">
            <summary>Used by OpJoinRoom and by OpCreateRoom alike.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpCreateRoom(Fusion.Photon.Realtime.EnterRoomParams)">
            <summary>
            Creates a room (on either Master or Game Server).
            The OperationResponse depends on the server the peer is connected to:
            Master will return a Game Server to connect to.
            Game Server will return the joined Room's data.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <remarks>
            If the room is already existing, the OperationResponse will have a returnCode of ErrorCode.GameAlreadyExists.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpJoinRoom(Fusion.Photon.Realtime.EnterRoomParams)">
            <summary>
            Joins a room by name or creates new room if room with given name not exists.
            The OperationResponse depends on the server the peer is connected to:
            Master will return a Game Server to connect to.
            Game Server will return the joined Room's data.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <remarks>
            If the room is not existing (anymore), the OperationResponse will have a returnCode of ErrorCode.GameDoesNotExist.
            Other possible ErrorCodes are: GameClosed, GameFull.
            </remarks>
            <returns>If the operation could be sent (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpJoinRandomRoom(Fusion.Photon.Realtime.OpJoinRandomRoomParams)">
            <summary>
            Operation to join a random, available room. Overloads take additional player properties.
            This is an async request which triggers a OnOperationResponse() call.
            If all rooms are closed or full, the OperationResponse will have a returnCode of ErrorCode.NoRandomMatchFound.
            If successful, the OperationResponse contains a gameserver address and the name of some room.
            </summary>
            <returns>If the operation could be sent currently (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpJoinRandomOrCreateRoom(Fusion.Photon.Realtime.OpJoinRandomRoomParams,Fusion.Photon.Realtime.EnterRoomParams)">
            <summary>
            Only used on the Master Server. It will assign a game server and room to join-or-create.
            On the Game Server, the OpJoin is used with option "create if not exists".
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpLeaveRoom(System.Boolean,System.Boolean)">
            <summary>
            Leaves a room with option to come back later or "for good".
            </summary>
            <param name="becomeInactive">Async games can be re-joined (loaded) later on. Set to false, if you want to abandon a game entirely.</param>
            <param name="sendAuthCookie">WebFlag: Securely transmit the encrypted object AuthCookie to the web service in PathLeave webhook when available</param>
            <returns>If the operation can be sent currently.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpGetGameList(Fusion.Photon.Realtime.TypedLobby,System.String)">
            <summary>Gets a list of games matching a SQL-like where clause.</summary>
            <remarks>
            Operation is only available in lobbies of type SqlLobby.
            This is an async request which triggers a OnOperationResponse() call.
            Returned game list is stored in RoomInfoList.
            </remarks>
            <see href="https://doc.photonengine.com/en-us/realtime/current/reference/matchmaking-and-lobby#sql_lobby_type"/>
            <param name="lobby">The lobby to query. Has to be of type SqlLobby.</param>
            <param name="queryData">The sql query statement.</param>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpFindFriends(System.String[],Fusion.Photon.Realtime.FindFriendsOptions)">
             <summary>
             Request the rooms and online status for a list of friends (each client must set a unique username via OpAuthenticate).
             </summary>
             <remarks>
             Used on Master Server to find the rooms played by a selected list of users.
             Users identify themselves by using OpAuthenticate with a unique user ID.
             The list of user IDs must be fetched from some other source (not provided by Photon).
            
             The server response includes 2 arrays of info (each index matching a friend from the request):<br/>
             ParameterCode.FindFriendsResponseOnlineList = bool[] of online states<br/>
             ParameterCode.FindFriendsResponseRoomIdList = string[] of room names (empty string if not in a room)<br/>
             <br/>
             The options may be used to define which state a room must match to be returned.
             </remarks>
             <param name="friendsToFind">Array of friend's names (make sure they are unique).</param>
             <param name="options">Options that affect the result of the FindFriends operation.</param>
             <returns>If the operation could be sent (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpSetPropertiesOfActor(System.Int32,ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
            <summary>
            Sets properties of a player / actor.
            Internally this uses OpSetProperties, which can be used to either set room or player properties.
            </summary>
            <param name="actorNr">The payer ID (a.k.a. actorNumber) of the player to attach these properties to.</param>
            <param name="actorProperties">The properties to add or update.</param>
            <param name="expectedProperties">If set, these must be in the current properties-set (on the server) to set actorProperties: CAS.</param>
            <param name="webflags">Set these to forward the properties to a WebHook as defined for this app (in Dashboard).</param>
            <returns>If the operation could be sent (requires connection).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpSetPropertiesOfRoom(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
            <summary>
            Sets properties of a room.
            Internally this uses OpSetProperties, which can be used to either set room or player properties.
            </summary>
            <param name="gameProperties">The properties to add or update.</param>
            <param name="expectedProperties">The properties expected when update occurs. (CAS : "Check And Swap")</param>
            <param name="webflags">WebFlag to indicate if request should be forwarded as "PathProperties" webhook or not.</param>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpAuthenticate(System.String,System.String,Fusion.Photon.Realtime.AuthenticationValues,System.String,System.Boolean)">
            <summary>
            Sends this app's appId and appVersion to identify this application server side.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <remarks>
            This operation makes use of encryption, if that is established before.
            See: EstablishEncryption(). Check encryption with IsEncryptionAvailable.
            This operation is allowed only once per connection (multiple calls will have ErrorCode != Ok).
            </remarks>
            <param name="appId">Your application's name or ID to authenticate. This is assigned by Photon Cloud (webpage).</param>
            <param name="appVersion">The client's version (clients with differing client appVersions are separated and players don't meet).</param>
            <param name="authValues">Contains all values relevant for authentication. Even without account system (external Custom Auth), the clients are allowed to identify themselves.</param>
            <param name="regionCode">Optional region code, if the client should connect to a specific Photon Cloud Region.</param>
            <param name="getLobbyStatistics">Set to true on Master Server to receive "Lobby Statistics" events.</param>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpAuthenticateOnce(System.String,System.String,Fusion.Photon.Realtime.AuthenticationValues,System.String,Fusion.Photon.Realtime.EncryptionMode,ExitGames.Client.Photon.ConnectionProtocol)">
            <summary>
            Sends this app's appId and appVersion to identify this application server side.
            This is an async request which triggers a OnOperationResponse() call.
            </summary>
            <remarks>
            This operation makes use of encryption, if that is established before.
            See: EstablishEncryption(). Check encryption with IsEncryptionAvailable.
            This operation is allowed only once per connection (multiple calls will have ErrorCode != Ok).
            </remarks>
            <param name="appId">Your application's name or ID to authenticate. This is assigned by Photon Cloud (webpage).</param>
            <param name="appVersion">The client's version (clients with differing client appVersions are separated and players don't meet).</param>
            <param name="authValues">Optional authentication values. The client can set no values or a UserId or some parameters for Custom Authentication by a server.</param>
            <param name="regionCode">Optional region code, if the client should connect to a specific Photon Cloud Region.</param>
            <param name="encryptionMode"></param>
            <param name="expectedProtocol"></param>
            <returns>If the operation could be sent (has to be connected).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpChangeGroups(System.Byte[],System.Byte[])">
             <summary>
             Operation to handle this client's interest groups (for events in room).
             </summary>
             <remarks>
             Note the difference between passing null and byte[0]:
               null won't add/remove any groups.
               byte[0] will add/remove all (existing) groups.
             First, removing groups is executed. This way, you could leave all groups and join only the ones provided.
            
             Changes become active not immediately but when the server executes this operation (approximately RTT/2).
             </remarks>
             <param name="groupsToRemove">Groups to remove from interest. Null will not remove any. A byte[0] will remove all.</param>
             <param name="groupsToAdd">Groups to add to interest. Null will not add any. A byte[0] will add all current.</param>
             <returns>If operation could be enqueued for sending. Sent when calling: Service or SendOutgoingCommands.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpRaiseEvent(System.Byte,System.Object,Fusion.Photon.Realtime.RaiseEventOptions,ExitGames.Client.Photon.SendOptions)">
            <summary>
            Send an event with custom code/type and any content to the other players in the same room.
            </summary>
            <remarks>This override explicitly uses another parameter order to not mix it up with the implementation for Hashtable only.</remarks>
            <param name="eventCode">Identifies this type of event (and the content). Your game's event codes can start with 0.</param>
            <param name="customEventContent">Any serializable datatype (including Hashtable like the other OpRaiseEvent overloads).</param>
            <param name="raiseEventOptions">Contains (slightly) less often used options. If you pass null, the default options will be used.</param>
            <param name="sendOptions">Send options for reliable, encryption etc</param>
            <returns>If operation could be enqueued for sending. Sent when calling: Service or SendOutgoingCommands.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.LoadBalancingPeer.OpSettings(System.Boolean)">
            <summary>
            Internally used operation to set some "per server" settings. This is for the Master Server.
            </summary>
            <param name="receiveLobbyStats">Set to true, to get Lobby Statistics (lists of existing lobbies).</param>
            <returns>False if the operation could not be sent.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.RoomOptionBit">
            <summary>Used in the RoomOptionFlags parameter, this bitmask toggles options in the room.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.FindFriendsOptions">
            <summary>
            Options for OpFindFriends can be combined to filter which rooms of friends are returned.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FindFriendsOptions.CreatedOnGs">
            <summary>Include a friend's room only if it is created and confirmed by the game server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FindFriendsOptions.Visible">
            <summary>Include a friend's room only if it is visible (using Room.IsVisible).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FindFriendsOptions.Open">
            <summary>Include a friend's room only if it is open (using Room.IsOpen).</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FindFriendsOptions.ToIntFlags">
            <summary>Turns the bool options into an integer, which is sent as option flags for Op FindFriends.</summary>
            <returns>The options applied to bits of an integer.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.OpJoinRandomRoomParams">
            <summary>
            Parameters for the matchmaking of JoinRandomRoom and JoinRandomOrCreateRoom.
            </summary>
            <remarks>
            More about matchmaking: <see href="https://doc.photonengine.com/en-us/pun/current/manuals-and-demos/matchmaking-and-lobby"/>.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.ExpectedCustomRoomProperties">
            <summary>The custom room properties a room must have to fit. All key-values must be present to match. In SQL Lobby, use SqlLobbyFilter instead.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.ExpectedMaxPlayers">
            <summary>Filters by the MaxPlayers value of rooms.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.MatchingType">
            <summary>The MatchmakingMode affects how rooms get filled. By default, the server fills rooms.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.TypedLobby">
            <summary>The lobby in which to match. The type affects how filters are applied.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.SqlLobbyFilter">
            <summary>SQL query to filter room matches. For default-typed lobbies, use ExpectedCustomRoomProperties instead.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.ExpectedUsers">
            <summary>The expected users list blocks player slots for your friends or team mates to join the room, too.</summary>
            <remarks>See: https://doc.photonengine.com/en-us/pun/v2/lobby-and-matchmaking/matchmaking-and-lobby#matchmaking_slot_reservation </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.OpJoinRandomRoomParams.Ticket">
            <summary>Ticket for matchmaking. Provided by a plugin / server and contains a list of party members who should join the same room (among other things).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.EnterRoomParams">
            <summary>Parameters for creating rooms.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.RoomName">
            <summary>The name of the room to create. If null, the server generates a unique name. If not null, it must be unique and new or will cause an error.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.RoomOptions">
            <summary>The RoomOptions define the optional behaviour of rooms.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.Lobby">
            <summary>A lobby to attach the new room to. If set, this overrides a joined lobby (if any).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.PlayerProperties">
            <summary>The custom player properties that describe this client / user. Keys must be strings.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.OnGameServer">
            <summary>Internally used value to skip some values when the operation is sent to the Master Server.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.JoinMode">
            <summary>Internally used value to check which join mode we should call.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.ExpectedUsers">
            <summary>A list of users who are expected to join the room along with this client. Reserves slots for rooms with MaxPlayers value.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EnterRoomParams.Ticket">
            <summary>Ticket for matchmaking. Provided by a plugin / server and contains a list of party members who should join the same room (among other things).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ErrorCode">
            <summary>
            ErrorCode defines the default codes associated with Photon client/server communication.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.Ok">
            <summary>(0) is always "OK", anything else an error or specific situation.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.OperationNotAllowedInCurrentState">
            <summary>
            (-3) Operation can't be executed yet (e.g. OpJoin can't be called before being authenticated, RaiseEvent cant be used before getting into a room).
            </summary>
            <remarks>
            Before you call any operations on the Cloud servers, the automated client workflow must complete its authorization.
            Wait until State is: JoinedLobby or ConnectedToMasterServer
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InvalidOperationCode">
            <summary>(-2) The operation you called is not implemented on the server (application) you connect to. Make sure you run the fitting applications.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InvalidOperation">
             <summary>(-2) The operation you called could not be executed on the server.</summary>
             <remarks>
             Make sure you are connected to the server you expect.
            
             This code is used in several cases:
             The arguments/parameters of the operation might be out of range, missing entirely or conflicting.
             The operation you called is not implemented on the server (application). Server-side plugins affect the available operations.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InternalServerError">
            <summary>(-1) Something went wrong in the server. Try to reproduce and contact Exit Games.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InvalidAuthentication">
            <summary>(32767) Authentication failed. Possible cause: AppId is unknown to Photon (in cloud service).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.GameIdAlreadyExists">
            <summary>(32766) GameId (name) already in use (can't create another). Change name.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.GameFull">
            <summary>(32765) Game is full. This rarely happens when some player joined the room before your join completed.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.GameClosed">
            <summary>(32764) Game is closed and can't be joined. Join another game.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.ServerFull">
             <summary>(32762) All servers are busy. This is a temporary issue and the game logic should try again after a brief wait time.</summary>
             <remarks>
             This error may happen for all operations that create rooms. The operation response will contain this error code.
            
             This error is very unlikely to happen as we monitor load on all servers and add them on demand.
             However, it's good to be prepared for a shortage of machines or surge in CCUs.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.UserBlocked">
            <summary>(32761) Not in use currently.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.NoRandomMatchFound">
            <summary>(32760) Random matchmaking only succeeds if a room exists thats neither closed nor full. Repeat in a few seconds or create a new room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.GameDoesNotExist">
            <summary>(32758) Join can fail if the room (name) is not existing (anymore). This can happen when players leave while you join.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.MaxCcuReached">
             <summary>(32757) Authorization on the Photon Cloud failed because the concurrent users (CCU) limit of the app's subscription is reached.</summary>
             <remarks>
             Unless you have a plan with "CCU Burst", clients might fail the authentication step during connect.
             Affected client are unable to call operations. Please note that players who end a game and return
             to the master server will disconnect and re-connect, which means that they just played and are rejected
             in the next minute / re-connect.
             This is a temporary measure. Once the CCU is below the limit, players will be able to connect an play again.
            
             OpAuthorize is part of connection workflow but only on the Photon Cloud, this error can happen.
             Self-hosted Photon servers with a CCU limited license won't let a client connect at all.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InvalidRegion">
             <summary>(32756) Authorization on the Photon Cloud failed because the app's subscription does not allow to use a particular region's server.</summary>
             <remarks>
             Some subscription plans for the Photon Cloud are region-bound. Servers of other regions can't be used then.
             Check your master server address and compare it with your Photon Cloud Dashboard's info.
             https://dashboard.photonengine.com
            
             OpAuthorize is part of connection workflow but only on the Photon Cloud, this error can happen.
             Self-hosted Photon servers with a CCU limited license won't let a client connect at all.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.CustomAuthenticationFailed">
            <summary>
            (32755) Custom Authentication of the user failed due to setup reasons (see Cloud Dashboard) or the provided user data (like username or token). Check error message for details.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.AuthenticationTicketExpired">
            <summary>(32753) The Authentication ticket expired. Usually, this is refreshed behind the scenes. Connect (and authorize) again.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.PluginReportedError">
            <summary>
            (32752) A server-side plugin or WebHook failed and reported an error. Check the OperationResponse.DebugMessage.
            </summary>
            <remarks>A typical case is when a plugin prevents a user from creating or joining a room.
            If this is prohibited, that reports to the client as a plugin error.<br/>
            Same for WebHooks.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.PluginMismatch">
            <summary>
            (32751) CreateGame/JoinGame/Join operation fails if expected plugin does not correspond to loaded one.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.JoinFailedPeerAlreadyJoined">
            <summary>
            (32750) for join requests. Indicates the current peer already called join and is joined to the room.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.JoinFailedFoundInactiveJoiner">
            <summary>
            (32749)  for join requests. Indicates the list of InactiveActors already contains an actor with the requested ActorNr or UserId.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.JoinFailedWithRejoinerNotFound">
            <summary>
            (32748) for join requests. Indicates the list of Actors (active and inactive) did not contain an actor with the requested ActorNr or UserId.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.JoinFailedFoundExcludedUserId">
            <summary>
            (32747) for join requests. Note: for future use - Indicates the requested UserId was found in the ExcludedList.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.JoinFailedFoundActiveJoiner">
            <summary>
            (32746) for join requests. Indicates the list of ActiveActors already contains an actor with the requested ActorNr or UserId.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.HttpLimitReached">
            <summary>
            (32745)  for SetProperties and RaiseEvent (if flag HttpForward is true) requests. Indicates the maximum allowed http requests per minute was reached.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.ExternalHttpCallFailed">
            <summary>
            (32744) for WebRpc requests. Indicates the the call to the external service failed.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.OperationLimitReached">
            <summary>
            (32743) for operations with defined limits (as in calls per second, content count or size).
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.SlotError">
            <summary>
            (32742) Server error during matchmaking with slot reservation. E.g. the reserved slots can not exceed MaxPlayers.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ErrorCode.InvalidEncryptionParameters">
            <summary>
            (32741) Server will react with this error if invalid encryption parameters provided by token
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ActorProperties">
            <summary>
            Class for constants. These (byte) values define "well known" properties for an Actor / Player.
            </summary>
            <remarks>
            These constants are used internally.
            "Custom properties" have to use a string-type as key. They can be assigned at will.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ActorProperties.PlayerName">
            <summary>(255) Name of a player/actor.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ActorProperties.IsInactive">
            <summary>(254) Tells you if the player is currently in this game (getting events live).</summary>
            <remarks>A server-set value for async games, where players can leave the game and return later.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ActorProperties.UserId">
            <summary>(253) UserId of the player. Sent when room gets created with RoomOptions.PublishUserId = true.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.GamePropertyKey">
            <summary>
            Class for constants. These (byte) values are for "well known" room/game properties used in Photon LoadBalancing.
            </summary>
            <remarks>
            These constants are used internally.
            "Custom properties" have to use a string-type as key. They can be assigned at will.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.MaxPlayers">
            <summary>(255) Max number of players that "fit" into this room. 0 is for "unlimited".</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.MaxPlayersInt">
            <summary>(243) Integer-typed max number of players that "fit" into a room. 0 is for "unlimited". Important: Code changed. See remarks.</summary>
            <remarks>This was code 244 for a brief time (Realtime v4.1.7.2 to v4.1.7.4) and those versions must be replaced or edited!</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.IsVisible">
            <summary>(254) Makes this room listed or not in the lobby on master.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.IsOpen">
            <summary>(253) Allows more players to join a room (or not).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.PlayerCount">
            <summary>(252) Current count of players in the room. Used only in the lobby on master.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.Removed">
            <summary>(251) True if the room is to be removed from room listing (used in update to room list in lobby on master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.PropsListedInLobby">
            <summary>(250) A list of the room properties to pass to the RoomInfo list in a lobby. This is used in CreateRoom, which defines this list once per room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.CleanupCacheOnLeave">
            <summary>(249) Equivalent of Operation Join parameter CleanupCacheOnLeave.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.MasterClientId">
            <summary>(248) Code for MasterClientId, which is synced by server. When sent as op-parameter this is (byte)203. As room property this is (byte)248.</summary>
            <remarks>Tightly related to ParameterCode.MasterClientId.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.ExpectedUsers">
            <summary>(247) Code for ExpectedUsers in a room. Matchmaking keeps a slot open for the players with these userIDs.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.PlayerTtl">
            <summary>(246) Player Time To Live. How long any player can be inactive (due to disconnect or leave) before the user gets removed from the playerlist (freeing a slot).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.GamePropertyKey.EmptyRoomTtl">
            <summary>(245) Room Time To Live. How long a room stays available (and in server-memory), after the last player becomes inactive. After this time, the room gets persisted or destroyed.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.EventCode">
            <summary>
            Class for constants. These values are for events defined by Photon LoadBalancing.
            </summary>
            <remarks>They start at 255 and go DOWN. Your own in-game events can start at 0. These constants are used internally.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.GameList">
            <summary>(230) Initial list of RoomInfos (in lobby on Master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.GameListUpdate">
            <summary>(229) Update of RoomInfos to be merged into "initial" list (in lobby on Master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.QueueState">
            <summary>(228) Currently not used. State of queueing in case of server-full</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.Match">
            <summary>(227) Currently not used. Event for matchmaking</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.AppStats">
            <summary>(226) Event with stats about this application (players, rooms, etc)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.LobbyStats">
            <summary>(224) This event provides a list of lobbies with their player and game counts.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.AzureNodeInfo">
            <summary>(210) Internally used in case of hosting by Azure</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.Join">
            <summary>(255) Event Join: someone joined the game. The new actorNumber is provided as well as the properties of that actor (if set in OpJoin).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.Leave">
            <summary>(254) Event Leave: The player who left the game can be identified by the actorNumber.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.PropertiesChanged">
            <summary>(253) When you call OpSetProperties with the broadcast option "on", this event is fired. It contains the properties being set.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.SetProperties">
            <summary>(253) When you call OpSetProperties with the broadcast option "on", this event is fired. It contains the properties being set.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.ErrorInfo">
            (252) When player left game unexpected and the room has a playerTtl != 0, this event is fired to let everyone know about the timeout.
            Obsolete. Replaced by Leave. public const byte Disconnect = LiteEventCode.Disconnect;
            <summary>(251) Sent by Photon Cloud when a plugin-call or webhook-call failed or events cache limit exceeded. Usually, the execution on the server continues, despite the issue. Contains: ParameterCode.Info.</summary>
            <seealso href="https://doc.photonengine.com/en-us/realtime/current/reference/webhooks#options"/>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.CacheSliceChanged">
            <summary>(250) Sent by Photon whent he event cache slice was changed. Done by OpRaiseEvent.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCode.AuthEvent">
            <summary>(223) Sent by Photon to update a token before it times out.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ParameterCode">
            <summary>Class for constants. Codes for parameters of Operations and Events.</summary>
            <remarks>These constants are used internally.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.SuppressRoomEvents">
            <summary>(237) A bool parameter for creating games. If set to true, no room events are sent to the clients on join and leave. Default: false (and not sent).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.EmptyRoomTTL">
            <summary>(236) Time To Live (TTL) for a room when the last player leaves. Keeps room in memory for case a player re-joins soon. In milliseconds.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PlayerTTL">
            <summary>(235) Time To Live (TTL) for an 'actor' in a room. If a client disconnects, this actor is inactive first and removed after this timeout. In milliseconds.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.EventForward">
            <summary>(234) Optional parameter of OpRaiseEvent and OpSetCustomProperties to forward the event/operation to a web-service.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.IsComingBack">
            <summary>(233) Optional parameter of OpLeave in async games. If false, the player does abandons the game (forever). By default players become inactive and can re-join.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.IsInactive">
            <summary>(233) Used in EvLeave to describe if a user is inactive (and might come back) or not. In rooms with PlayerTTL, becoming inactive is the default case.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.CheckUserOnJoin">
            <summary>(232) Used when creating rooms to define if any userid can join the room only once.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ExpectedValues">
            <summary>(231) Code for "Check And Swap" (CAS) when changing properties.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Address">
            <summary>(230) Address of a (game) server to use.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PeerCount">
            <summary>(229) Count of players in this application in a rooms (used in stats event)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.GameCount">
            <summary>(228) Count of games in this application (used in stats event)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.MasterPeerCount">
            <summary>(227) Count of players on the master server (in this app, looking for rooms)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.UserId">
            <summary>(225) User's ID</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ApplicationId">
            <summary>(224) Your application's ID: a name on your own Photon or a GUID on the Photon Cloud</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Position">
            <summary>(223) Not used currently (as "Position"). If you get queued before connect, this is your position</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.MatchMakingType">
            <summary>(223) Modifies the matchmaking algorithm used for OpJoinRandom. Allowed parameter values are defined in enum MatchmakingMode.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.GameList">
            <summary>(222) List of RoomInfos about open / listed rooms</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Token">
            <summary>(221) Internally used to establish encryption</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.AppVersion">
            <summary>(220) Version of your application</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.AzureNodeInfo">
            <summary>(210) Internally used in case of hosting by Azure</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.AzureLocalNodeId">
            <summary>(209) Internally used in case of hosting by Azure</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.AzureMasterNodeId">
            <summary>(208) Internally used in case of hosting by Azure</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.RoomName">
            <summary>(255) Code for the gameId/roomName (a unique name per room). Used in OpJoin and similar.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Broadcast">
            <summary>(250) Code for broadcast parameter of OpSetProperties method.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ActorList">
            <summary>(252) Code for list of players in a room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ActorNr">
            <summary>(254) Code of the Actor of an operation. Used for property get and set.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PlayerProperties">
            <summary>(249) Code for property set (Hashtable).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.CustomEventContent">
            <summary>(245) Code of data/custom content of an event. Used in OpRaiseEvent.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Data">
            <summary>(245) Code of data of an event. Used in OpRaiseEvent.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Code">
            <summary>(244) Code used when sending some code-related parameter, like OpRaiseEvent's event-code.</summary>
            <remarks>This is not the same as the Operation's code, which is no longer sent as part of the parameter Dictionary in Photon 3.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.GameProperties">
            <summary>(248) Code for property set (Hashtable).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Properties">
            <summary>
            (251) Code for property-set (Hashtable). This key is used when sending only one set of properties.
            If either ActorProperties or GameProperties are used (or both), check those keys.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.TargetActorNr">
            <summary>(253) Code of the target Actor of an operation. Used for property set. Is 0 for game</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ReceiverGroup">
            <summary>(246) Code to select the receivers of events (used in Lite, Operation RaiseEvent).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Cache">
            <summary>(247) Code for caching events while raising them.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.CleanupCacheOnLeave">
            <summary>(241) Bool parameter of CreateGame Operation. If true, server cleans up roomcache of leaving players (their cached events get removed).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Group">
            <summary>(240) Code for "group" operation-parameter (as used in Op RaiseEvent).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Remove">
            <summary>(239) The "Remove" operation-parameter can be used to remove something from a list. E.g. remove groups from player's interest groups.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PublishUserId">
            <summary>(239) Used in Op Join to define if UserIds of the players are broadcast in the room. Useful for FindFriends and reserving slots for expected users.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Add">
            <summary>(238) The "Add" operation-parameter can be used to add something to some list or set. E.g. add groups to player's interest groups.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Info">
            <summary>(218) Content for EventCode.ErrorInfo and internal debug operations.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ClientAuthenticationType">
            <summary>(217) This key's (byte) value defines the target custom authentication type/service the client connects with. Used in OpAuthenticate</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ClientAuthenticationParams">
            <summary>(216) This key's (string) value provides parameters sent to the custom authentication type/service the client connects with. Used in OpAuthenticate</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.JoinMode">
            <summary>(215) The JoinMode enum defines which variant of joining a room will be executed: Join only if available, create if not exists or re-join.</summary>
            <remarks>Replaces CreateIfNotExists which was only a bool-value.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ClientAuthenticationData">
            <summary>(214) This key's (string or byte[]) value provides parameters sent to the custom authentication service setup in Photon Dashboard. Used in OpAuthenticate</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.MasterClientId">
            <summary>(203) Code for MasterClientId, which is synced by server. When sent as op-parameter this is code 203.</summary>
            <remarks>Tightly related to GamePropertyKey.MasterClientId.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.FindFriendsRequestList">
            <summary>(1) Used in Op FindFriends request. Value must be string[] of friends to look up.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.FindFriendsOptions">
            <summary>(2) Used in Op FindFriends request. An integer containing option-flags to filter the results.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.FindFriendsResponseOnlineList">
            <summary>(1) Used in Op FindFriends response. Contains bool[] list of online states (false if not online).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.FindFriendsResponseRoomIdList">
            <summary>(2) Used in Op FindFriends response. Contains string[] of room names ("" where not known or no room joined).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.LobbyName">
            <summary>(213) Used in matchmaking-related methods and when creating a room to name a lobby (to join or to attach a room to).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.LobbyType">
            <summary>(212) Used in matchmaking-related methods and when creating a room to define the type of a lobby. Combined with the lobby name this identifies the lobby.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.LobbyStats">
            <summary>(211) This (optional) parameter can be sent in Op Authenticate to turn on Lobby Stats (info about lobby names and their user- and game-counts).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Region">
            <summary>(210) Used for region values in OpAuth and OpGetRegions.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.UriPath">
            <summary>(209) Path of the WebRPC that got called. Also known as "WebRpc Name". Type: string.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.WebRpcParameters">
            <summary>(208) Parameters for a WebRPC as: Dictionary&lt;string, object&gt;. This will get serialized to JSon.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.WebRpcReturnCode">
            <summary>(207) ReturnCode for the WebRPC, as sent by the web service (not by Photon, which uses ErrorCode). Type: byte.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.WebRpcReturnMessage">
            <summary>(206) Message returned by WebRPC server. Analog to Photon's debug message. Type: string.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.CacheSliceIndex">
            <summary>(205) Used to define a "slice" for cached events. Slices can easily be removed from cache. Type: int.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Plugins">
            <summary>(204) Informs the server of the expected plugin setup.</summary>
            <remarks>
            The operation will fail in case of a plugin mismatch returning error code PluginMismatch 32751(0x7FFF - 16).
            Setting string[]{} means the client expects no plugin to be setup.
            Note: for backwards compatibility null omits any check.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.NickName">
            <summary>(202) Used by the server in Operation Responses, when it sends the nickname of the client (the user's nickname).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PluginName">
            <summary>(201) Informs user about name of plugin load to game</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.PluginVersion">
            <summary>(200) Informs user about version of plugin load to game</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Cluster">
            <summary>(196) Cluster info provided in OpAuthenticate/OpAuthenticateOnce responses.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ExpectedProtocol">
            <summary>(195) Protocol which will be used by client to connect master/game servers. Used for nameserver.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.CustomInitData">
            <summary>(194) Set of custom parameters which are sent in auth request.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.EncryptionMode">
            <summary>(193) How are we going to encrypt data.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.EncryptionData">
            <summary>(192) Parameter of Authentication, which contains encryption keys (depends on AuthMode and EncryptionMode).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.RoomOptionFlags">
            <summary>(191) An int parameter summarizing several boolean room-options with bit-flags.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.Ticket">
            <summary>Matchmaking ticket (type object).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.MatchMakingGroupId">
            <summary>Used server side once the group is extracted from the ticket. Clients don't send this.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.AllowRepeats">
            <summary>(188) Parameter key to let the server know it may queue the client in low-ccu matchmaking situations.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ParameterCode.ReportQos">
            <summary>(187) This optional parameter from the server configures the client to send analytics or not.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.OperationCode">
            <summary>
            Class for constants. Contains operation codes.
            </summary>
            <remarks>These constants are used internally.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.Join">
            <summary>(255) Code for OpJoin, to get into a room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.AuthenticateOnce">
            <summary>(231) Authenticates this peer and connects to a virtual application</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.Authenticate">
            <summary>(230) Authenticates this peer and connects to a virtual application</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.JoinLobby">
            <summary>(229) Joins lobby (on master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.LeaveLobby">
            <summary>(228) Leaves lobby (on master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.CreateGame">
            <summary>(227) Creates a game (or fails if name exists)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.JoinGame">
            <summary>(226) Join game (by name)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.JoinRandomGame">
            <summary>(225) Joins random game (on master)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.Leave">
            <summary>(254) Code for OpLeave, to get out of a room.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.RaiseEvent">
            <summary>(253) Raise event (in a room, for other actors/players)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.SetProperties">
            <summary>(252) Set Properties (of room or actor/player)</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.GetProperties">
            <summary>(251) Get Properties</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.ChangeGroups">
            <summary>(248) Operation code to change interest groups in Rooms (Lite application and extending ones).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.FindFriends">
            <summary>(222) Request the rooms and online status for a list of friends (by name, which should be unique).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.GetLobbyStats">
            <summary>(221) Request statistics about a specific list of lobbies (their user and game count).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.GetRegions">
            <summary>(220) Get list of regional servers from a NameServer.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.WebRpc">
            <summary>(219) WebRpc Operation.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.ServerSettings">
            <summary>(218) Operation to set some server settings. Used with different parameters on various servers.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.OperationCode.GetGameList">
            <summary>(217) Get the game list matching a supplied sql filter (SqlListLobby only) </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.JoinMode">
            <summary>Defines possible values for OpJoinRoom and OpJoinOrCreate. It tells the server if the room can be only be joined normally, created implicitly or found on a web-service for Turnbased games.</summary>
            <remarks>These values are not directly used by a game but implicitly set.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinMode.Default">
            <summary>Regular join. The room must exist.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinMode.CreateIfNotExists">
            <summary>Join or create the room if it's not existing. Used for OpJoinOrCreate for example.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinMode.JoinOrRejoin">
            <summary>The room might be out of memory and should be loaded (if possible) from a Turnbased web-service.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.JoinMode.RejoinOnly">
            <summary>Only re-join will be allowed. If the user is not yet in the room, this will fail.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.MatchmakingMode">
            <summary>
            Options for matchmaking rules for OpJoinRandom.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.MatchmakingMode.FillRoom">
            <summary>Fills up rooms (oldest first) to get players together as fast as possible. Default.</summary>
            <remarks>Makes most sense with MaxPlayers > 0 and games that can only start with more players.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.MatchmakingMode.SerialMatching">
            <summary>Distributes players across available rooms sequentially but takes filter into account. Without filter, rooms get players evenly distributed.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.MatchmakingMode.RandomMatching">
            <summary>Joins a (fully) random room. Expected properties must match but aside from this, any available room might be selected.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.ReceiverGroup">
            <summary>
            Lite - OpRaiseEvent lets you chose which actors in the room should receive events.
            By default, events are sent to "Others" but you can overrule this.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ReceiverGroup.Others">
            <summary>Default value (not sent). Anyone else gets my event.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ReceiverGroup.All">
            <summary>Everyone in the current room (including this peer) will get this event.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.ReceiverGroup.MasterClient">
            <summary>The server sends this event only to the actor with the lowest actorNumber.</summary>
            <remarks>The "master client" does not have special rights but is the one who is in this room the longest time.</remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.EventCaching">
            <summary>
            Lite - OpRaiseEvent allows you to cache events and automatically send them to joining players in a room.
            Events are cached per event code and player: Event 100 (example!) can be stored once per player.
            Cached events can be modified, replaced and removed.
            </summary>
            <remarks>
            Caching works only combination with ReceiverGroup options Others and All.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.DoNotCache">
            <summary>Default value (not sent).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.MergeCache">
            <summary>Will merge this event's keys with those already cached.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.ReplaceCache">
            <summary>Replaces the event cache for this eventCode with this event's content.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.RemoveCache">
            <summary>Removes this event (by eventCode) from the cache.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.AddToRoomCache">
            <summary>Adds an event to the room's cache</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.AddToRoomCacheGlobal">
            <summary>Adds this event to the cache for actor 0 (becoming a "globally owned" event in the cache).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.RemoveFromRoomCache">
            <summary>Remove fitting event from the room's cache.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.RemoveFromRoomCacheForActorsLeft">
            <summary>Removes events of players who already left the room (cleaning up).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.SliceIncreaseIndex">
            <summary>Increase the index of the sliced cache.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.SliceSetIndex">
            <summary>Set the index of the sliced cache. You must set RaiseEventOptions.CacheSliceIndex for this.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.SlicePurgeIndex">
            <summary>Purge cache slice with index. Exactly one slice is removed from cache. You must set RaiseEventOptions.CacheSliceIndex for this.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.EventCaching.SlicePurgeUpToIndex">
            <summary>Purge cache slices with specified index and anything lower than that. You must set RaiseEventOptions.CacheSliceIndex for this.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.PropertyTypeFlag">
            <summary>
            Flags for "types of properties", being used as filter in OpGetProperties.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PropertyTypeFlag.None">
            <summary>(0x00) Flag type for no property type.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PropertyTypeFlag.Game">
            <summary>(0x01) Flag type for game-attached properties.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PropertyTypeFlag.Actor">
            <summary>(0x02) Flag type for actor related propeties.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PropertyTypeFlag.GameAndActor">
            <summary>(0x01) Flag type for game AND actor properties. Equal to 'Game'</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.RoomOptions">
            <summary>Wraps up common room properties needed when you create rooms. Read the individual entries for more details.</summary>
            <remarks>This directly maps to the fields in the Room class.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.IsVisible">
             <summary>Defines if this room is listed in the lobby. If not, it also is not joined randomly.</summary>
             <remarks>
             A room that is not visible will be excluded from the room lists that are sent to the clients in lobbies.
             An invisible room can be joined by name but is excluded from random matchmaking.
            
             Use this to "hide" a room and simulate "private rooms". Players can exchange a roomname and create it
             invisble to avoid anyone else joining it.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.IsOpen">
            <summary>Defines if this room can be joined at all.</summary>
            <remarks>
            If a room is closed, no player can join this. As example this makes sense when 3 of 4 possible players
            start their gameplay early and don't want anyone to join during the game.
            The room can still be listed in the lobby (set isVisible to control lobby-visibility).
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.MaxPlayers">
            <summary>Max number of players that can be in the room at any time. 0 means "no limit".</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.PlayerTtl">
            <summary>Time To Live (TTL) for an 'actor' in a room. If a client disconnects, this actor is inactive first and removed after this timeout. In milliseconds.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.EmptyRoomTtl">
            <summary>Time To Live (TTL) for a room when the last player leaves. Keeps room in memory for case a player re-joins soon. In milliseconds.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.CleanupCacheOnLeave">
            <summary>Removes a user's events and properties from the room when a user leaves.</summary>
            <remarks>
            This makes sense when in rooms where players can't place items in the room and just vanish entirely.
            When you disable this, the event history can become too long to load if the room stays in use indefinitely.
            Default: true. Cleans up the cache and props of leaving users.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.CustomRoomProperties">
            <summary>The room's custom properties to set. Use string keys!</summary>
            <remarks>
            Custom room properties are any key-values you need to define the game's setup.
            The shorter your keys are, the better.
            Example: Map, Mode (could be "m" when used with "Map"), TileSet (could be "t").
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.CustomRoomPropertiesForLobby">
             <summary>Defines the custom room properties that get listed in the lobby.</summary>
             <remarks>
             Name the custom room properties that should be available to clients that are in a lobby.
             Use with care. Unless a custom property is essential for matchmaking or user info, it should
             not be sent to the lobby, which causes traffic and delays for clients in the lobby.
            
             Default: No custom properties are sent to the lobby.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomOptions.Plugins">
            <summary>Informs the server of the expected plugin setup.</summary>
            <remarks>
            The operation will fail in case of a plugin missmatch returning error code PluginMismatch 32757(0x7FFF - 10).
            Setting string[]{} means the client expects no plugin to be setup.
            Note: for backwards compatibility null omits any check.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.SuppressRoomEvents">
            <summary>
            Tells the server to skip room events for joining and leaving players.
            </summary>
            <remarks>
            Using this makes the client unaware of the other players in a room.
            That can save some traffic if you have some server logic that updates players
            but it can also limit the client's usability.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.SuppressPlayerInfo">
            <summary>Disables events join and leave from the server as well as property broadcasts in a room (to minimize traffic)</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.PublishUserId">
            <summary>
            Defines if the UserIds of players get "published" in the room. Useful for FindFriends, if players want to play another game together.
            </summary>
            <remarks>
            When you set this to true, Photon will publish the UserIds of the players in that room.
            In that case, you can use PhotonPlayer.userId, to access any player's userID.
            This is useful for FindFriends and to set "expected users" to reserve slots in a room.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.DeleteNullProperties">
             <summary>Optionally, properties get deleted, when null gets assigned as value. Defaults to off / false.</summary>
             <remarks>
             When Op SetProperties is setting a key's value to null, the server and clients should remove the key/value from the Custom Properties.
             By default, the server keeps the keys (and null values) and sends them to joining players.
            
             Important: Only when SetProperties does a "broadcast", the change (key, value = null) is sent to clients to update accordingly.
             This applies to Custom Properties for rooms and actors/players.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomOptions.BroadcastPropsChangeToAll">
             <summary>By default, property changes are sent back to the client that's setting them to avoid de-sync when properties are set concurrently.</summary>
             <remarks>
             This option is enables by default to fix this scenario:
            
             1) On server, room property ABC is set to value FOO, which triggers notifications to all the clients telling them that the property changed.
             2) While that notification is in flight, a client sets the ABC property to value BAR.
             3) Client receives notification from the server and changes its local copy of ABC to FOO.
             4) Server receives the set operation and changes the official value of ABC to BAR, but never notifies the client that sent the set operation that the value is now BAR.
            
             Without this option, the client that set the value to BAR never hears from the server that the official copy has been updated to BAR, and thus gets stuck with a value of FOO.
             </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.RaiseEventOptions">
            <summary>Aggregates several less-often used options for operation RaiseEvent. See field descriptions for usage details.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.Default">
            <summary>Default options: CachingOption: DoNotCache, InterestGroup: 0, targetActors: null, receivers: Others, sequenceChannel: 0.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.CachingOption">
            <summary>Defines if the server should simply send the event, put it in the cache or remove events that are like this one.</summary>
            <remarks>
            When using option: SliceSetIndex, SlicePurgeIndex or SlicePurgeUpToIndex, set a CacheSliceIndex. All other options except SequenceChannel get ignored.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.InterestGroup">
            <summary>The number of the Interest Group to send this to. 0 goes to all users but to get 1 and up, clients must subscribe to the group first.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.TargetActors">
            <summary>A list of Player.ActorNumbers to send this event to. You can implement events that just go to specific users this way.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.Receivers">
            <summary>Sends the event to All, MasterClient or Others (default). Be careful with MasterClient, as the client might disconnect before it got the event and it gets lost.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.SequenceChannel">
            <summary>Events are ordered per "channel". If you have events that are independent of others, they can go into another sequence or channel.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RaiseEventOptions.Flags">
            <summary> Optional flags to be used in Photon client SDKs with Op RaiseEvent and Op SetProperties.</summary>
            <remarks>Introduced mainly for webhooks 1.2 to control behavior of forwarded HTTP requests.</remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.LobbyType">
            <summary>Types of lobbies define their behaviour and capabilities. Check each value for details.</summary>
            <remarks>Values of this enum must be matched by the server.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.LobbyType.Default">
            <summary>Standard type and behaviour: While joined to this lobby clients get room-lists and JoinRandomRoom can use a simple filter to match properties (perfectly).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LobbyType.SqlLobby">
            <summary>This lobby type lists rooms like Default but JoinRandom has a parameter for SQL-like "where" clauses for filtering. This allows bigger, less, or and and combinations.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.LobbyType.AsyncRandomLobby">
            <summary>This lobby does not send lists of games. It is only used for OpJoinRandomRoom. It keeps rooms available for a while when there are only inactive users left.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.TypedLobby">
            <summary>Refers to a specific lobby on the server.</summary>
            <remarks>
            Name and Type combined are the unique identifier for a lobby.<br/>
            The server will create lobbies "on demand", so no registration or setup is required.<br/>
            An empty or null Name always points to the "default lobby" as special case.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.TypedLobby.Name">
            <summary>
            Name of the lobby. Default: null, pointing to the "default lobby".
            </summary>
            <remarks>
            If Name is null or empty, a TypedLobby will point to the "default lobby". This ignores the Type value and always acts as  <see cref="F:Fusion.Photon.Realtime.LobbyType.Default"/>.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.TypedLobby.Type">
            <summary>
            Type (and behaviour) of the lobby.
            </summary>
            <remarks>
            An empty or null Name always points to the "default lobby" as special case.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.TypedLobby.Default">
            <summary>
            A reference to the default lobby which is the unique lobby that uses null as name and is of type <see cref="F:Fusion.Photon.Realtime.LobbyType.Default"/>.
            </summary>
            <remarks>
            There is only a single lobby with an empty name on the server. It is always of type  <see cref="F:Fusion.Photon.Realtime.LobbyType.Default"/>.<br/>
            On the other hand, this is a shortcut and reusable reference to the default lobby.<br/>
            Do not change Name or Type.<br/>
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.TypedLobby.IsDefault">
            <summary>
            Returns whether or not this instance points to the "default lobby" (<see cref="F:Fusion.Photon.Realtime.TypedLobby.Default"/>).
            </summary>
            <remarks>
            This comes up to checking if the Name is null or empty.
            <see cref="F:Fusion.Photon.Realtime.LobbyType.Default"/> is not the same thing as the "default lobby" (<see cref="F:Fusion.Photon.Realtime.TypedLobby.Default"/>).
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.TypedLobby.#ctor">
            <summary>
            Creates a TypedLobby instance. Unless Name is changed, this points to the "default lobby" (<see cref="F:Fusion.Photon.Realtime.TypedLobby.Default"/>).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.TypedLobby.#ctor(System.String,Fusion.Photon.Realtime.LobbyType)">
            <summary>
            Sets Name and Type of the new instance. Make sure name is not empty or null, as that always points to the "default lobby" (<see cref="F:Fusion.Photon.Realtime.TypedLobby.Default"/>).
            </summary>
            <param name="name">Some string to identify a lobby.</param>
            <param name="type">The type of a lobby defines it's capabilities and behaviour.</param>
        </member>
        <member name="T:Fusion.Photon.Realtime.TypedLobbyInfo">
            <summary>
            Info for a lobby on the server. Used when <see cref="F:Fusion.Photon.Realtime.LoadBalancingClient.EnableLobbyStatistics"/> is true.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.TypedLobbyInfo.PlayerCount">
            <summary>Count of players that currently joined this lobby.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.TypedLobbyInfo.RoomCount">
            <summary>Count of rooms currently associated with this lobby.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.AuthModeOption">
            <summary>
            Options for authentication modes. From "classic" auth on each server to AuthOnce (on NameServer).
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AuthModeOption.Auth">
            <summary>
            Classic authentication mode.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AuthModeOption.AuthOnce">
            <summary>
            Authenticate once on the NameServer.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.AuthModeOption.AuthOnceWss">
            <summary>
            Authenticate once on the NameServer using WebSocket Secure (WSS).
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.CustomAuthenticationType">
            <summary>
            Options for optional "Custom Authentication" services used with Photon. Used by OpAuthenticate after connecting to Photon.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Custom">
            <summary>Use a custom authentication service. Currently the only implemented option.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Steam">
            <summary>Authenticates users by their Steam Account. Set Steam's ticket as "ticket" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Facebook">
            <summary>Authenticates users by their Facebook Account.  Set Facebooks's tocken as "token" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Oculus">
            <summary>Authenticates users by their Oculus Account and token. Set Oculus' userid as "userid" and nonce as "nonce" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.PlayStation4">
            <summary>Authenticates users by their PSN Account and token on PS4. Set token as "token", env as "env" and userName as "userName" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.PlayStation">
            <inheritdoc cref="F:Fusion.Photon.Realtime.CustomAuthenticationType.PlayStation4"/>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Xbox">
            <summary>Authenticates users by their Xbox Account. Pass the XSTS token via SetAuthPostData().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Viveport">
            <summary>Authenticates users by their HTC Viveport Account. Set userToken as "userToken" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.NintendoSwitch">
            <summary>Authenticates users by their NSA ID. Set token  as "token" and appversion as "appversion" via AddAuthParameter(). The appversion is optional.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.PlayStation5">
            <summary>Authenticates users by their PSN Account and token on PS5. Set token as "token", env as "env" and userName as "userName" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Playstation5">
            <inheritdoc cref="F:Fusion.Photon.Realtime.CustomAuthenticationType.PlayStation5"/>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.Epic">
            <summary>Authenticates users with Epic Online Services (EOS). Set token as "token" and ownershipToken as "ownershipToken" via AddAuthParameter(). The ownershipToken is optional.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.FacebookGaming">
            <summary>Authenticates users with Facebook Gaming api. Set token as "token" via AddAuthParameter().</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.CustomAuthenticationType.None">
            <summary>Disables custom authentication. Same as not providing any AuthenticationValues for connect (more precisely for: OpAuthenticate).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.AuthenticationValues">
             <summary>
             Container for user authentication in Photon. Set AuthValues before you connect - all else is handled.
             </summary>
             <remarks>
             On Photon, user authentication is optional but can be useful in many cases.
             If you want to FindFriends, a unique ID per user is very practical.
            
             There are basically three options for user authentication: None at all, the client sets some UserId
             or you can use some account web-service to authenticate a user (and set the UserId server-side).
            
             Custom Authentication lets you verify end-users by some kind of login or token. It sends those
             values to Photon which will verify them before granting access or disconnecting the client.
            
             The AuthValues are sent in OpAuthenticate when you connect, so they must be set before you connect.
             If the AuthValues.UserId is null or empty when it's sent to the server, then the Photon Server assigns a UserId!
            
             The Photon Cloud Dashboard will let you enable this feature and set important server values for it.
             https://dashboard.photonengine.com
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.AuthenticationValues.authType">
            <summary>See AuthType.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.AuthenticationValues.AuthType">
            <summary>The type of authentication provider that should be used. Defaults to None (no auth whatsoever).</summary>
            <remarks>Several auth providers are available and CustomAuthenticationType.Custom can be used if you build your own service.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.AuthenticationValues.AuthGetParameters">
            <summary>This string must contain any (http get) parameters expected by the used authentication service. By default, username and token.</summary>
            <remarks>
            Maps to operation parameter 216.
            Standard http get parameters are used here and passed on to the service that's defined in the server (Photon Cloud Dashboard).
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.AuthenticationValues.AuthPostData">
            <summary>Data to be passed-on to the auth service via POST. Default: null (not sent). Either string or byte[] (see setters).</summary>
            <remarks>Maps to operation parameter 214.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.AuthenticationValues.Token">
            <summary>Internal <b>Photon token</b>. After initial authentication, Photon provides a token for this client, subsequently used as (cached) validation.</summary>
            <remarks>Any token for custom authentication should be set via SetAuthPostData or AddAuthParameter.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.AuthenticationValues.UserId">
            <summary>The UserId should be a unique identifier per user. This is for finding friends, etc..</summary>
            <remarks>See remarks of AuthValues for info about how this is set and used.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.#ctor">
            <summary>Creates empty auth values without any info.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.#ctor(System.String)">
            <summary>Creates minimal info about the user. If this is authenticated or not, depends on the set AuthType.</summary>
            <param name="userId">Some UserId to set in Photon.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.SetAuthPostData(System.String)">
            <summary>Sets the data to be passed-on to the auth service via POST.</summary>
            <remarks>AuthPostData is just one value. Each SetAuthPostData replaces any previous value. It can be either a string, a byte[] or a dictionary.</remarks>
            <param name="stringData">String data to be used in the body of the POST request. Null or empty string will set AuthPostData to null.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.SetAuthPostData(System.Byte[])">
            <summary>Sets the data to be passed-on to the auth service via POST.</summary>
            <remarks>AuthPostData is just one value. Each SetAuthPostData replaces any previous value. It can be either a string, a byte[] or a dictionary.</remarks>
            <param name="byteData">Binary token / auth-data to pass on.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.SetAuthPostData(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>Sets data to be passed-on to the auth service as Json (Content-Type: "application/json") via Post.</summary>
            <remarks>AuthPostData is just one value. Each SetAuthPostData replaces any previous value. It can be either a string, a byte[] or a dictionary.</remarks>
            <param name="dictData">A authentication-data dictionary will be converted to Json and passed to the Auth webservice via HTTP Post.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.AddAuthParameter(System.String,System.String)">
            <summary>Adds a key-value pair to the get-parameters used for Custom Auth (AuthGetParameters).</summary>
            <remarks>This method does uri-encoding for you.</remarks>
            <param name="key">Key for the value to set.</param>
            <param name="value">Some value relevant for Custom Authentication.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.ToString">
            <summary>
            Transform this object into string.
            </summary>
            <returns>String info about this object's values.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.AuthenticationValues.CopyTo(Fusion.Photon.Realtime.AuthenticationValues)">
            <summary>
            Make a copy of the current object.
            </summary>
            <param name="copy">The object to be copied into.</param>
            <returns>The copied object.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.PhotonPing">
            <summary>
            Abstract implementation of PhotonPing, ase for pinging servers to find the "Best Region".
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.DebugString">
            <summary>Caches the last exception/error message, if any.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.Successful">
            <summary>True of the ping was successful.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.GotResult">
            <summary>True if there was any result.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.PingLength">
            <summary>Length of a ping.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.PingBytes">
            <summary>Bytes to send in a (Photon UDP) ping.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonPing.PingId">
            <summary>Randomized number to identify a ping.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PhotonPing.StartPing(System.String)">
            <summary>Begins sending a ping.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PhotonPing.Done">
            <summary>Check if done.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PhotonPing.Dispose">
            <summary>Dispose of this ping.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PhotonPing.Init">
            <summary>Initialize this ping (GotResult, Successful, PingId).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.PingMono">
            <summary>Uses C# Socket class from System.Net.Sockets (as Unity usually does).</summary>
            <remarks>Incompatible with Windows 8 Store/Phone API.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.PingMono.StartPing(System.String)">
            <summary>
            Sends a "Photon Ping" to a server.
            </summary>
            <param name="ip">Address in IPv4 or IPv6 format. An address containing a '.' will be interpreted as IPv4.</param>
            <returns>True if the Photon Ping could be sent.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.PingMono.Done">
            <summary>Check if done.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PingMono.Dispose">
            <summary>Dispose of this ping.</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.Player">
            <summary>
            Summarizes a "player" within a room, identified (in that room) by ID (or "actorNumber").
            </summary>
            <remarks>
            Each player has a actorNumber, valid for that room. It's -1 until assigned by server (and client logic).
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.RoomReference">
            <summary>
            Used internally to identify the masterclient of a room.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.Player.actorNumber">
            <summary>Backing field for property.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.ActorNumber">
            <summary>Identifier of this player in current room. Also known as: actorNumber or actorNumber. It's -1 outside of rooms.</summary>
            <remarks>The ID is assigned per room and only valid in that context. It will change even on leave and re-join. IDs are never re-used per room.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.Player.IsLocal">
            <summary>Only one player is controlled by each client. Others are not local.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.Player.nickName">
            <summary>Background field for nickName.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.NickName">
            <summary>Non-unique nickname of this player. Synced automatically in a room.</summary>
            <remarks>
            A player might change his own playername in a room (it's only a property).
            Setting this value updates the server and other players (using an operation).
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.UserId">
            <summary>UserId of the player, available when the room got created with RoomOptions.PublishUserId = true.</summary>
            <remarks>Useful for <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpFindFriends(System.String[],Fusion.Photon.Realtime.FindFriendsOptions)"/> and blocking slots in a room for expected players (e.g. in <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpCreateRoom(Fusion.Photon.Realtime.EnterRoomParams)"/>).</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.IsMasterClient">
            <summary>
            True if this player is the Master Client of the current room.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.IsInactive">
            <summary>If this player is active in the room (and getting events which are currently being sent).</summary>
            <remarks>
            Inactive players keep their spot in a room but otherwise behave as if offline (no matter what their actual connection status is).
            The room needs a PlayerTTL != 0. If a player is inactive for longer than PlayerTTL, the server will remove this player from the room.
            For a client "rejoining" a room, is the same as joining it: It gets properties, cached events and then the live events.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Player.CustomProperties">
            <summary>Read-only cache for custom properties of player. Set via Player.SetCustomProperties.</summary>
            <remarks>
            Don't modify the content of this Hashtable. Use SetCustomProperties and the
            properties of this class to modify values. When you use those, the client will
            sync values with the server.
            </remarks>
            <see cref="M:Fusion.Photon.Realtime.Player.SetCustomProperties(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)"/>
        </member>
        <member name="F:Fusion.Photon.Realtime.Player.TagObject">
            <summary>Can be used to store a reference that's useful to know "by player".</summary>
            <remarks>Example: Set a player's character as Tag by assigning the GameObject on Instantiate.</remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.#ctor(System.String,System.Int32,System.Boolean)">
            <summary>
            Creates a player instance.
            To extend and replace this Player, override LoadBalancingPeer.CreatePlayer().
            </summary>
            <param name="nickName">NickName of the player (a "well known property").</param>
            <param name="actorNumber">ID or ActorNumber of this player in the current room (a shortcut to identify each player in room)</param>
            <param name="isLocal">If this is the local peer's player (or a remote one).</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.#ctor(System.String,System.Int32,System.Boolean,ExitGames.Client.Photon.Hashtable)">
            <summary>
            Creates a player instance.
            To extend and replace this Player, override LoadBalancingPeer.CreatePlayer().
            </summary>
            <param name="nickName">NickName of the player (a "well known property").</param>
            <param name="actorNumber">ID or ActorNumber of this player in the current room (a shortcut to identify each player in room)</param>
            <param name="isLocal">If this is the local peer's player (or a remote one).</param>
            <param name="playerProperties">A Hashtable of custom properties to be synced. Must use String-typed keys and serializable datatypes as values.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.Get(System.Int32)">
            <summary>
            Get a Player by ActorNumber (Player.ID).
            </summary>
            <param name="id">ActorNumber of the a player in this room.</param>
            <returns>Player or null.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.GetNext">
            <summary>Gets this Player's next Player, as sorted by ActorNumber (Player.ID). Wraps around.</summary>
            <returns>Player or null.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.GetNextFor(Fusion.Photon.Realtime.Player)">
            <summary>Gets a Player's next Player, as sorted by ActorNumber (Player.ID). Wraps around.</summary>
            <remarks>Useful when you pass something to the next player. For example: passing the turn to the next player.</remarks>
            <param name="currentPlayer">The Player for which the next is being needed.</param>
            <returns>Player or null.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.GetNextFor(System.Int32)">
            <summary>Gets a Player's next Player, as sorted by ActorNumber (Player.ID). Wraps around.</summary>
            <remarks>Useful when you pass something to the next player. For example: passing the turn to the next player.</remarks>
            <param name="currentPlayerId">The ActorNumber (Player.ID) for which the next is being needed.</param>
            <returns>Player or null.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.InternalCacheProperties(ExitGames.Client.Photon.Hashtable)">
            <summary>Caches properties for new Players or when updates of remote players are received. Use SetCustomProperties() for a synced update.</summary>
            <remarks>
            This only updates the CustomProperties and doesn't send them to the server.
            Mostly used when creating new remote players, where the server sends their properties.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.ToString">
            <summary>
            Brief summary string of the Player: ActorNumber and NickName
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.ToStringFull">
            <summary>
            String summary of the Player: player.ID, name and all custom properties of this user.
            </summary>
            <remarks>
            Use with care and not every frame!
            Converts the customProperties to a String on every single call.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.Equals(System.Object)">
            <summary>
            If players are equal (by GetHasCode, which returns this.ID).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.GetHashCode">
            <summary>
            Accompanies Equals, using the ID (actorNumber) as HashCode to return.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.ChangeLocalID(System.Int32)">
            <summary>
            Used internally, to update this client's playerID when assigned (doesn't change after assignment).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.SetCustomProperties(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
             <summary>
             Updates and synchronizes this Player's Custom Properties. Optionally, expectedProperties can be provided as condition.
             </summary>
             <remarks>
             Custom Properties are a set of string keys and arbitrary values which is synchronized
             for the players in a Room. They are available when the client enters the room, as
             they are in the response of OpJoin and OpCreate.
            
             Custom Properties either relate to the (current) Room or a Player (in that Room).
            
             Both classes locally cache the current key/values and make them available as
             property: CustomProperties. This is provided only to read them.
             You must use the method SetCustomProperties to set/modify them.
            
             Any client can set any Custom Properties anytime (when in a room).
             It's up to the game logic to organize how they are best used.
            
             You should call SetCustomProperties only with key/values that are new or changed. This reduces
             traffic and performance.
            
             Unless you define some expectedProperties, setting key/values is always permitted.
             In this case, the property-setting client will not receive the new values from the server but
             instead update its local cache in SetCustomProperties.
            
             If you define expectedProperties, the server will skip updates if the server property-cache
             does not contain all expectedProperties with the same values.
             In this case, the property-setting client will get an update from the server and update it's
             cached key/values at about the same time as everyone else.
            
             The benefit of using expectedProperties can be only one client successfully sets a key from
             one known value to another.
             As example: Store who owns an item in a Custom Property "ownedBy". It's 0 initally.
             When multiple players reach the item, they all attempt to change "ownedBy" from 0 to their
             actorNumber. If you use expectedProperties {"ownedBy", 0} as condition, the first player to
             take the item will have it (and the others fail to set the ownership).
            
             Properties get saved with the game state for Turnbased games (which use IsPersistent = true).
             </remarks>
             <param name="propertiesToSet">Hashtable of Custom Properties to be set. </param>
             <param name="expectedValues">If non-null, these are the property-values the server will check as condition for this update.</param>
             <param name="webFlags">Defines if this SetCustomProperties-operation gets forwarded to your WebHooks. Client must be in room.</param>
             <returns>
             False if propertiesToSet is null or empty or have zero string keys.
             True in offline mode even if expectedProperties or webFlags are used.
             If not in a room, returns true if local player and expectedValues and webFlags are null.
             (Use this to cache properties to be sent when joining a room).
             Otherwise, returns if this operation could be sent to the server.
             </returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.UpdateNickNameOnJoined">
            <summary>If there is a nickname in the room props, but it's not the current (local) one, update the room when joining/joined.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Player.SetPlayerNameProperty">
            <summary>Uses OpSetPropertiesOfActor to sync this player's NickName (server is being updated with this.NickName).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Region.Cluster">
            <summary>Unlike the CloudRegionCode, this may contain cluster information.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Region.Ping">
            <summary>Weighted ping time.</summary>
            <remarks>
            Regions gets pinged 5 times (RegionPinger.Attempts).
            Out of those, the worst rtt is discarded and the best will be counted two times for a weighted average.
            </remarks>
        </member>
        <member name="T:Fusion.Photon.Realtime.RegionHandler">
             <summary>
             Provides methods to work with Photon's regions (Photon Cloud) and can be use to find the one with best ping.
             </summary>
             <remarks>
             When a client uses a Name Server to fetch the list of available regions, the LoadBalancingClient will create a RegionHandler
             and provide it via the OnRegionListReceived callback, as soon as the list is available. No pings were sent for Best Region selection yet.
            
             Your logic can decide to either connect to one of those regional servers, or it may use PingMinimumOfRegions to test
             which region provides the best ping. Alternatively the client may be set to connect to the Best Region (lowest ping), one or
             more regions get pinged.
             Not all regions will be pinged. As soon as the results are final, the client will connect to the best region,
             so you can check the ping results when connected to the Master Server.
            
             Regions gets pinged 5 times (RegionPinger.Attempts).
             Out of those, the worst rtt is discarded and the best will be counted two times for a weighted average.
            
             Usually UDP will be used to ping the Master Servers. In WebGL, WSS is used instead.
            
             It makes sense to make clients "sticky" to a region when one gets selected.
             This can be achieved by storing the SummaryToCache value, once pinging was done.
             When the client connects again, the previous SummaryToCache helps limiting the number of regions to ping.
             In best case, only the previously selected region gets re-pinged and if the current ping is not much worse, this one region is used again.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionHandler.PingImplementation">
            <summary>The implementation of PhotonPing to use for region pinging (Best Region detection).</summary>
            <remarks>Defaults to null, which means the Type is set automatically.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionHandler.EnabledRegions">
            <summary>A list of region names for the Photon Cloud. Set by the result of OpGetRegions().</summary>
            <remarks>
            Implement ILoadBalancingCallbacks and register for the callbacks to get OnRegionListReceived(RegionHandler regionHandler).
            You can also put a "case OperationCode.GetRegions:" into your OnOperationResponse method to notice when the result is available.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionHandler.BestRegion">
            <summary>
            When PingMinimumOfRegions was called and completed, the BestRegion is identified by best ping.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionHandler.SummaryToCache">
            <summary>
            This value summarizes the results of pinging currently available regions (after PingMinimumOfRegions finished).
            </summary>
            <remarks>
            This value should be stored in the client by the game logic.
            When connecting again, use it as previous summary to speed up pinging regions and to make the best region sticky for the client.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.GetResults">
            <summary>Provides a list of regions and their pings as string.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.SetRegions(ExitGames.Client.Photon.OperationResponse,Fusion.Photon.Realtime.LoadBalancingClient)">
            <summary>Initializes the regions of this RegionHandler with values provided from the Name Server (as OperationResponse for OpGetRegions).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionHandler.PortToPingOverride">
            <summary>If non-zero, this port will be used to ping Master Servers on.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionHandler.rePingFactor">
            <summary>If the previous Best Region's ping is now higher by this much, ping all regions and find a new Best Region.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionHandler.pingSimilarityFactor">
            <summary>How much higher a region's ping can be from the absolute best, to be considered the Best Region (by ping and name).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionHandler.BestRegionSummaryPingLimit">
            <summary>If the region from a previous BestRegionSummary now has a ping higher than this limit, all regions get pinged again to find a better. Default: 90ms.</summary>
            <remarks>
            Pinging all regions takes time, which is why a BestRegionSummary gets stored.
            If that is available, the Best Region becomes sticky and is used again.
            This limit introduces an exception: Should the pre-defined best region have a ping worse than this, all regions are considered.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionHandler.IsPinging">
            <summary>True if the available regions are being pinged currently.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionHandler.Aborted">
            <summary>True if the pinging of regions is being aborted.</summary>
            <see cref="M:Fusion.Photon.Realtime.RegionHandler.Abort"/>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.#ctor(System.UInt16)">
            <summary>Creates a new RegionHandler.</summary>
            <param name="masterServerPortOverride">If non-zero, this port will be used to ping Master Servers on.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.PingMinimumOfRegions(System.Action{Fusion.Photon.Realtime.RegionHandler},System.String)">
            <summary>Starts the process of pinging of all available regions.</summary>
            <param name="onCompleteCallback">Provide a method to call when all ping results are available. Aborting the pings will also cancel the callback.</param>
            <param name="previousSummary">A BestRegionSummary from an earlier RegionHandler run. This makes a selected best region "sticky" and keeps ping times lower.</param>
            <returns>If pining the regions gets started now. False if the current state prevent this.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.Abort">
            <summary>Calling this will stop pinging the regions and suppress the onComplete callback.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionHandler.PingEnabledRegions">
            <summary>Privately used to ping regions if the current best one isn't as fast as earlier.</summary>
            <returns>If pinging can be started.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.RegionPinger">
            <summary>Wraps the ping attempts and workflow for a single region.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionPinger.Attempts">
            <summary>How often to ping a region.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionPinger.MaxMillisecondsPerPing">
            <summary>How long to wait maximum for a response.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionPinger.PingWhenFailed">
            <summary>Ping result when pinging failed.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RegionPinger.CurrentAttempt">
            <summary>Current ping attempt count.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionPinger.Done">
            <summary>True if all attempts are done or timed out.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RegionPinger.Aborted">
            <summary>Set to true to abort pining this region.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.#ctor(Fusion.Photon.Realtime.Region,System.Action{Fusion.Photon.Realtime.Region})">
            <summary>Initializes a RegionPinger for the given region.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.GetPingImplementation">
            <summary>Selects the best fitting ping implementation or uses the one set in RegionHandler.PingImplementation.</summary>
            <returns>PhotonPing instance to use.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.Start">
            <summary>
            Starts the ping routine for the assigned region.
            </summary>
            <remarks>
            Pinging runs in a ThreadPool worker item or (if needed) in a Thread.
            WebGL runs pinging on the Main Thread as coroutine.
            </remarks>
            <returns>True unless Aborted.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.Abort">
            <summary>Calling this will stop pinging the regions and cancel the onComplete callback.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.RegionPingThreaded">
            <summary>Pings the region. To be called by a thread.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.RegionPingCoroutine">
            <remarks>
            Affected by frame-rate of app, as this Coroutine checks the socket for a result once per frame.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.GetResults">
            <summary>Gets this region's results as string summary.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RegionPinger.ResolveHost(System.String)">
            <summary>
            Attempts to resolve a hostname into an IP string or returns empty string if that fails.
            </summary>
            <remarks>
            To be compatible with most platforms, the address family is checked like this:<br/>
            if (ipAddress.AddressFamily.ToString().Contains("6")) // ipv6...
            </remarks>
            <param name="hostName">Hostname to resolve.</param>
            <returns>IP string or empty string if resolution fails</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.Room">
             <summary>
             This class represents a room a client joins/joined.
             </summary>
             <remarks>
             Contains a list of current players, their properties and those of this room, too.
             A room instance has a number of "well known" properties like IsOpen, MaxPlayers which can be changed.
             Your own, custom properties can be set via SetCustomProperties() while being in the room.
            
             Typically, this class should be extended by a game-specific implementation with logic and extra features.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.LoadBalancingClient">
            <summary>
            A reference to the LoadBalancingClient which is currently keeping the connection and state.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.Name">
            <summary>The name of a room. Unique identifier (per region and virtual appid) for a room/match.</summary>
            <remarks>The name can't be changed once it's set by the server.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.IsOpen">
             <summary>
             Defines if the room can be joined.
             </summary>
             <remarks>
             This does not affect listing in a lobby but joining the room will fail if not open.
             If not open, the room is excluded from random matchmaking.
             Due to racing conditions, found matches might become closed while users are trying to join.
             Simply re-connect to master and find another.
             Use property "IsVisible" to not list the room.
            
             As part of RoomInfo this can't be set.
             As part of a Room (which the player joined), the setter will update the server and all clients.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.IsVisible">
             <summary>
             Defines if the room is listed in its lobby.
             </summary>
             <remarks>
             Rooms can be created invisible, or changed to invisible.
             To change if a room can be joined, use property: open.
            
             As part of RoomInfo this can't be set.
             As part of a Room (which the player joined), the setter will update the server and all clients.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.MaxPlayers">
            <summary>
            Sets a limit of players to this room. This property is synced and shown in lobby, too.
            If the room is full (players count == maxplayers), joining this room will fail.
            </summary>
            <remarks>
            As part of RoomInfo this can't be set.
            As part of a Room (which the player joined), the setter will update the server and all clients.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.PlayerCount">
            <summary>The count of players in this Room (using this.Players.Count).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.Room.players">
            <summary>While inside a Room, this is the list of players who are also in that room.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.Players">
            <summary>While inside a Room, this is the list of players who are also in that room.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.ExpectedUsers">
            <summary>
            List of users who are expected to join this room. In matchmaking, Photon blocks a slot for each of these UserIDs out of the MaxPlayers.
            </summary>
            <remarks>
            The corresponding feature in Photon is called "Slot Reservation" and can be found in the doc pages.
            Define expected players in the methods: <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpCreateRoom(Fusion.Photon.Realtime.EnterRoomParams)"/>, <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinRoom(Fusion.Photon.Realtime.EnterRoomParams)"/> and <see cref="M:Fusion.Photon.Realtime.LoadBalancingClient.OpJoinRandomRoom(Fusion.Photon.Realtime.OpJoinRandomRoomParams)"/>.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.PlayerTtl">
            <summary>Player Time To Live. How long any player can be inactive (due to disconnect or leave) before the user gets removed from the playerlist (freeing a slot).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.EmptyRoomTtl">
            <summary>Room Time To Live. How long a room stays available (and in server-memory), after the last player becomes inactive. After this time, the room gets persisted or destroyed.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.MasterClientId">
            <summary>
            The ID (actorNumber, actorNumber) of the player who's the master of this Room.
            Note: This changes when the current master leaves the room.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.PropertiesListedInLobby">
            <summary>
            Gets a list of custom properties that are in the RoomInfo of the Lobby.
            This list is defined when creating the room and can't be changed afterwards. Compare: LoadBalancingClient.OpCreateRoom()
            </summary>
            <remarks>You could name properties that are not set from the beginning. Those will be synced with the lobby when added later on.</remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.AutoCleanUp">
             <summary>
             Gets if this room cleans up the event cache when a player (actor) leaves.
             </summary>
             <remarks>
             This affects which events joining players get.
            
             Set in room creation via RoomOptions.CleanupCacheOnLeave.
            
             Within PUN, auto cleanup of events means that cached RPCs and instantiated networked objects are deleted from the room.
             </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.BroadcastPropertiesChangeToAll">
            <summary>Define if the client who calls SetProperties should receive the properties update event or not. </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.SuppressRoomEvents">
            <summary>Define if Join and Leave events should not be sent to clients in the room. </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.SuppressPlayerInfo">
            <summary>Extends SuppressRoomEvents: Define if Join and Leave events but also the actors' list and their respective properties should not be sent to clients. </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.PublishUserId">
            <summary>Define if UserIds of the players are broadcast in the room. Useful for FindFriends and reserving slots for expected users.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.Room.DeleteNullProperties">
            <summary>Define if actor or room properties with null values are removed on the server or kept.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.#ctor(System.String,Fusion.Photon.Realtime.RoomOptions,System.Boolean)">
            <summary>Creates a Room (representation) with given name and properties and the "listing options" as provided by parameters.</summary>
            <param name="roomName">Name of the room (can be null until it's actually created on server).</param>
            <param name="options">Room options.</param>
            <param name="isOffline">Signal if this room is only used locally.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.InternalCacheRoomFlags(System.Int32)">
            <summary>Read (received) room option flags into related bool parameters.</summary>
            <remarks>This is for internal use. The operation response for join and create room operations is read this way.</remarks>
            <param name="roomFlags"></param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.SetCustomProperties(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)">
             <summary>
             Updates and synchronizes this Room's Custom Properties. Optionally, expectedProperties can be provided as condition.
             </summary>
             <remarks>
             Custom Properties are a set of string keys and arbitrary values which is synchronized
             for the players in a Room. They are available when the client enters the room, as
             they are in the response of OpJoin and OpCreate.
            
             Custom Properties either relate to the (current) Room or a Player (in that Room).
            
             Both classes locally cache the current key/values and make them available as
             property: CustomProperties. This is provided only to read them.
             You must use the method SetCustomProperties to set/modify them.
            
             Any client can set any Custom Properties anytime (when in a room).
             It's up to the game logic to organize how they are best used.
            
             You should call SetCustomProperties only with key/values that are new or changed. This reduces
             traffic and performance.
            
             Unless you define some expectedProperties, setting key/values is always permitted.
             In this case, the property-setting client will not receive the new values from the server but
             instead update its local cache in SetCustomProperties.
            
             If you define expectedProperties, the server will skip updates if the server property-cache
             does not contain all expectedProperties with the same values.
             In this case, the property-setting client will get an update from the server and update it's
             cached key/values at about the same time as everyone else.
            
             The benefit of using expectedProperties can be only one client successfully sets a key from
             one known value to another.
             As example: Store who owns an item in a Custom Property "ownedBy". It's 0 initally.
             When multiple players reach the item, they all attempt to change "ownedBy" from 0 to their
             actorNumber. If you use expectedProperties {"ownedBy", 0} as condition, the first player to
             take the item will have it (and the others fail to set the ownership).
            
             Properties get saved with the game state for Turnbased games (which use IsPersistent = true).
             </remarks>
             <param name="propertiesToSet">Hashtable of Custom Properties that changes.</param>
             <param name="expectedProperties">Provide some keys/values to use as condition for setting the new values. Client must be in room.</param>
             <param name="webFlags">Defines if this SetCustomProperties-operation gets forwarded to your WebHooks. Client must be in room.</param>
             <returns>
             False if propertiesToSet is null or empty or have zero string keys.
             True in offline mode even if expectedProperties or webFlags are used.
             Otherwise, returns if this operation could be sent to the server.
             </returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.SetPropertiesListedInLobby(System.String[])">
            <summary>
            Enables you to define the properties available in the lobby if not all properties are needed to pick a room.
            </summary>
            <remarks>
            Limit the amount of properties sent to users in the lobby to improve speed and stability.
            </remarks>
            <param name="lobbyProps">An array of custom room property names to forward to the lobby.</param>
            <returns>If the operation could be sent to the server.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.RemovePlayer(Fusion.Photon.Realtime.Player)">
            <summary>
            Removes a player from this room's Players Dictionary.
            This is internally used by the LoadBalancing API. There is usually no need to remove players yourself.
            This is not a way to "kick" players.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.RemovePlayer(System.Int32)">
            <summary>
            Removes a player from this room's Players Dictionary.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.SetMasterClient(Fusion.Photon.Realtime.Player)">
             <summary>
             Asks the server to assign another player as Master Client of your current room.
             </summary>
             <remarks>
             RaiseEvent has the option to send messages only to the Master Client of a room.
             SetMasterClient affects which client gets those messages.
            
             This method calls an operation on the server to set a new Master Client, which takes a roundtrip.
             In case of success, this client and the others get the new Master Client from the server.
            
             SetMasterClient tells the server which current Master Client should be replaced with the new one.
             It will fail, if anything switches the Master Client moments earlier. There is no callback for this
             error. All clients should get the new Master Client assigned by the server anyways.
            
             See also: MasterClientId
             </remarks>
             <param name="masterClientPlayer">The player to become the next Master Client.</param>
             <returns>False when this operation couldn't be done currently. Requires a v4 Photon Server.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.AddPlayer(Fusion.Photon.Realtime.Player)">
            <summary>
            Checks if the player is in the room's list already and calls StorePlayer() if not.
            </summary>
            <param name="player">The new player - identified by ID.</param>
            <returns>False if the player could not be added (cause it was in the list already).</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.StorePlayer(Fusion.Photon.Realtime.Player)">
            <summary>
            Updates a player reference in the Players dictionary (no matter if it existed before or not).
            </summary>
            <param name="player">The Player instance to insert into the room.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.GetPlayer(System.Int32,System.Boolean)">
            <summary>
            Tries to find the player with given actorNumber (a.k.a. ID).
            Only useful when in a Room, as IDs are only valid per Room.
            </summary>
            <param name="id">ID to look for.</param>
            <param name="findMaster">If true, the Master Client is returned for ID == 0.</param>
            <returns>The player with the ID or null.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.ClearExpectedUsers">
             <summary>
             Attempts to remove all current expected users from the server's Slot Reservation list.
             </summary>
             <remarks>
             Note that this operation can conflict with new/other users joining. They might be
             adding users to the list of expected users before or after this client called ClearExpectedUsers.
            
             This room's expectedUsers value will update, when the server sends a successful update.
            
             Internals: This methods wraps up setting the ExpectedUsers property of a room.
             </remarks>
             <returns>If the operation could be sent to the server.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.SetExpectedUsers(System.String[])">
             <summary>
             Attempts to update the expected users from the server's Slot Reservation list.
             </summary>
             <remarks>
             Note that this operation can conflict with new/other users joining. They might be
             adding users to the list of expected users before or after this client called SetExpectedUsers.
            
             This room's expectedUsers value will update, when the server sends a successful update.
            
             Internals: This methods wraps up setting the ExpectedUsers property of a room.
             </remarks>
             <param name="newExpectedUsers">The new array of UserIDs to be reserved in the room.</param>
             <returns>If the operation could be sent to the server.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.ToString">
            <summary>Returns a summary of this Room instance as string.</summary>
            <returns>Summary of this Room instance.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.Room.ToStringFull">
            <summary>Returns a summary of this Room instance as longer string, including Custom Properties.</summary>
            <returns>Summary of this Room instance.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.RoomInfo">
            <summary>
            A simplified room with just the info required to list and join, used for the room listing in the lobby.
            The properties are not settable (IsOpen, MaxPlayers, etc).
            </summary>
            <remarks>
            This class resembles info about available rooms, as sent by the Master server's lobby.
            Consider all values as readonly. None are synced (only updated by events by server).
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.RemovedFromList">
            <summary>Used in lobby, to mark rooms that are no longer listed (for being full, closed or hidden).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.customProperties">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.maxPlayers">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.emptyRoomTtl">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.playerTtl">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.expectedUsers">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.isOpen">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.isVisible">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.autoCleanUp">
            <summary>Backing field for property. False unless the GameProperty is set to true (else it's not sent).</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.name">
            <summary>Backing field for property.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.masterClientId">
            <summary>Backing field for master client id (actorNumber). defined by server in room props and ev leave.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.RoomInfo.propertiesListedInLobby">
            <summary>Backing field for property.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.CustomProperties">
            <summary>Read-only "cache" of custom properties of a room. Set via Room.SetCustomProperties (not available for RoomInfo class!).</summary>
            <remarks>All keys are string-typed and the values depend on the game/application.</remarks>
            <see cref="M:Fusion.Photon.Realtime.Room.SetCustomProperties(ExitGames.Client.Photon.Hashtable,ExitGames.Client.Photon.Hashtable,Fusion.Photon.Realtime.WebFlags)"/>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.Name">
            <summary>The name of a room. Unique identifier for a room/match (per AppId + game-Version).</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.PlayerCount">
            <summary>
            Count of players currently in room. This property is overwritten by the Room class (used when you're in a Room).
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.MaxPlayers">
            <summary>
            The limit of players for this room. This property is shown in lobby, too.
            If the room is full (players count == maxplayers), joining this room will fail.
            </summary>
            <remarks>
            As part of RoomInfo this can't be set.
            As part of a Room (which the player joined), the setter will update the server and all clients.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.IsOpen">
            <summary>
            Defines if the room can be joined.
            This does not affect listing in a lobby but joining the room will fail if not open.
            If not open, the room is excluded from random matchmaking.
            Due to racing conditions, found matches might become closed even while you join them.
            Simply re-connect to master and find another.
            Use property "IsVisible" to not list the room.
            </summary>
            <remarks>
            As part of RoomInfo this can't be set.
            As part of a Room (which the player joined), the setter will update the server and all clients.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.RoomInfo.IsVisible">
            <summary>
            Defines if the room is listed in its lobby.
            Rooms can be created invisible, or changed to invisible.
            To change if a room can be joined, use property: open.
            </summary>
            <remarks>
            As part of RoomInfo this can't be set.
            As part of a Room (which the player joined), the setter will update the server and all clients.
            </remarks>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.#ctor(System.String,ExitGames.Client.Photon.Hashtable)">
            <summary>
            Constructs a RoomInfo to be used in room listings in lobby.
            </summary>
            <param name="roomName">Name of the room and unique ID at the same time.</param>
            <param name="roomProperties">Properties for this room.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.Equals(System.Object)">
            <summary>
            Makes RoomInfo comparable (by name).
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.GetHashCode">
            <summary>
            Accompanies Equals, using the name's HashCode as return.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.ToString">
            <summary>Returns most interesting room values as string.</summary>
            <returns>Summary of this RoomInfo instance.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.ToStringFull">
            <summary>Returns most interesting room values as string, including custom properties.</summary>
            <returns>Summary of this RoomInfo instance.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.RoomInfo.InternalCacheProperties(ExitGames.Client.Photon.Hashtable)">
            <summary>Copies "well known" properties to fields (IsVisible, etc) and caches the custom properties (string-keys only) in a local hashtable.</summary>
            <param name="propertiesToCache">New or updated properties to store in this RoomInfo.</param>
        </member>
        <member name="T:Fusion.Photon.Realtime.SupportLogger">
            <summary>
            Helper class to debug log basic information about Photon client and vital traffic statistics.
            </summary>
            <remarks>
            Set SupportLogger.Client for this to work.
            </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.SupportLogger.LogTrafficStats">
            <summary>
            Toggle to enable or disable traffic statistics logging.
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.SupportLogger.initialOnApplicationPauseSkipped">
            helps skip the initial OnApplicationPause call, which is not really of interest on start
        </member>
        <member name="P:Fusion.Photon.Realtime.SupportLogger.Client">
            <summary>
            Photon client to log information and statistics from.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.SupportLogger.LogStats">
            <summary>
            Debug logs vital traffic statistics about the attached Photon Client.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.SupportLogger.LogBasics">
            <summary>
            Debug logs basic information (AppId, AppVersion, PeerID, Server address, Region) about the attached Photon Client.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.SystemConnectionSummary">
             <summary>
             The SystemConnectionSummary (SBS) is useful to analyze low level connection issues in Unity. This requires a ConnectionHandler in the scene.
             </summary>
             <remarks>
             A LoadBalancingClient automatically creates a SystemConnectionSummary on these disconnect causes:
             DisconnectCause.ExceptionOnConnect, DisconnectCause.Exception, DisconnectCause.ServerTimeout and DisconnectCause.ClientTimeout.
            
             The SBS can then be turned into an integer (ToInt()) or string to debug the situation or use in analytics.
             Both, ToString and ToInt summarize the network-relevant conditions of the client at and before the connection fail, including the PhotonPeer.SocketErrorCode.
            
             Important: To correctly create the SBS instance, a ConnectionHandler component must be present and enabled in the
             Unity scene hierarchy. In best case, keep the ConnectionHandler on a GameObject which is flagged as
             DontDestroyOnLoad.
             </remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.SystemConnectionSummary.SCSBitPos.Version">
            <summary>28 and up. 4 bits.</summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.SystemConnectionSummary.SCSBitPos.UsedProtocol">
            <summary>25 and up. 3 bits.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.#ctor(Fusion.Photon.Realtime.LoadBalancingClient)">
             <summary>
             Creates a SystemConnectionSummary for an incident of a local LoadBalancingClient. This gets used automatically by the LoadBalancingClient!
             </summary>
             <remarks>
             If the LoadBalancingClient.SystemConnectionSummary is non-null after a connection-loss, you can call .ToInt() and send this to analytics or log it.
            
             </remarks>
             <param name="client"></param>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.#ctor(System.Int32)">
            <summary>
            Creates a SystemConnectionSummary instance from an int (reversing ToInt()). This can then be turned into a string again.
            </summary>
            <param name="summary">An int, as provided by ToInt(). No error checks yet.</param>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.ToInt">
            <summary>
            Turns the SystemConnectionSummary into an integer, which can be be used for analytics purposes. It contains a lot of info and can be used to instantiate a new SystemConnectionSummary.
            </summary>
            <returns>Compact representation of the context for a disconnect issue.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.ToString">
            <summary>
            A readable debug log string of the context for network problems.
            </summary>
            <returns>SystemConnectionSummary as readable string.</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.SetBit(System.Int32@,System.Boolean,System.Int32)">
            <summary>Applies bitval to bitpos (no matter value's initial bit value).</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.SystemConnectionSummary.SetBits(System.Int32@,System.Byte,System.Int32)">
            <summary>Applies bitvals via OR operation (expects bits in value to be 0 initially).</summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.WebRpcResponse">
            <summary>Reads an operation response of a WebRpc and provides convenient access to most common values.</summary>
            <remarks>
            See LoadBalancingClient.OpWebRpc.<br/>
            Create a WebRpcResponse to access common result values.<br/>
            The operationResponse.OperationCode should be: OperationCode.WebRpc.<br/>
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebRpcResponse.Name">
            <summary>Name of the WebRpc that was called.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebRpcResponse.ResultCode">
            <summary>ResultCode of the WebService that answered the WebRpc.</summary>
            <remarks>
             0 is: "OK" for WebRPCs.<br/>
            -1 is: No ResultCode by WebRpc service (check <see cref="F:ExitGames.Client.Photon.OperationResponse.ReturnCode"/>).<br/>
            Other ResultCode are defined by the individual WebRpc and service.
            </remarks>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebRpcResponse.Message">
            <summary>Might be empty or null.</summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebRpcResponse.Parameters">
            <summary>Other key/values returned by the webservice that answered the WebRpc.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.WebRpcResponse.#ctor(ExitGames.Client.Photon.OperationResponse)">
            <summary>An OperationResponse for a WebRpc is needed to read it's values.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.WebRpcResponse.ToStringFull">
            <summary>Turns the response into an easier to read string.</summary>
            <returns>String resembling the result.</returns>
        </member>
        <member name="T:Fusion.Photon.Realtime.WebFlags">
            <summary>
            Optional flags to be used in Photon client SDKs with Op RaiseEvent and Op SetProperties.
            Introduced mainly for webhooks 1.2 to control behavior of forwarded HTTP requests.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebFlags.HttpForward">
            <summary>
            Indicates whether to forward HTTP request to web service or not.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebFlags.SendAuthCookie">
            <summary>
            Indicates whether to send AuthCookie of actor in the HTTP request to web service or not.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebFlags.SendSync">
            <summary>
            Indicates whether to send HTTP request synchronously or asynchronously to web service.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.WebFlags.SendState">
            <summary>
            Indicates whether to send serialized game state in HTTP request to web service or not.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.FusionAppSettings">
            <summary>
            Settings for Fusion application
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FusionAppSettings.encryptionMode">
            <summary>
            Select the Relayed Connection Encryption Mode
            </summary>
        </member>
        <member name="F:Fusion.Photon.Realtime.FusionAppSettings.emptyRoomTtl">
            <summary>
            Photon Session EmptyRoomTTL in milliseconds. Valid only in Shared Mode.
            </summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionAppSettings.GetCopy">
            <summary>
            Returns a copy of the current object.
            </summary>
            <returns>Copy of the current object</returns>
        </member>
        <member name="M:Fusion.Photon.Realtime.FusionAppSettings.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Fusion.Photon.Realtime.PhotonAppSettings">
            <summary>
            Collection of connection-relevant settings, used internally by PhotonNetwork.ConnectUsingSettings.
            </summary>
            <remarks>
            Includes the AppSettings class from the Realtime APIs plus some other, PUN-relevant, settings.</remarks>
        </member>
        <member name="F:Fusion.Photon.Realtime.PhotonAppSettings.AppSettings">
            <summary>
            The photon settings class, which is wrapped by this <see cref="T:Fusion.Photon.Realtime.PhotonAppSettings"/> ScriptableObject.
            </summary>
        </member>
        <member name="P:Fusion.Photon.Realtime.PhotonAppSettings.Global">
            <summary>Serialized server settings, written by the Setup Wizard for use in ConnectUsingSettings.</summary>
        </member>
        <member name="M:Fusion.Photon.Realtime.PhotonAppSettings.TryGetGlobal(Fusion.Photon.Realtime.PhotonAppSettings@)">
            <summary>
            Try to get the global settings
            </summary>
            <param name="settings">Output settings</param>
            <returns>True if the settings are loaded, false otherwise</returns>
        </member>
        <member name="P:Fusion.Photon.Realtime.PhotonAppSettings.IsGlobalLoaded">
            <summary>
            Check if the global settings are loaded
            </summary>
        </member>
        <member name="T:Fusion.SessionProperty">
            <summary>
            Holds a Custom Session Property value
            </summary>
        </member>
        <member name="P:Fusion.SessionProperty.PropertyValue">
            <summary>
            Internal stored value 
            </summary>
        </member>
        <member name="P:Fusion.SessionProperty.PropertyType">
            <summary>
            Get the Type of the internal stored value 
            </summary>
        </member>
        <member name="P:Fusion.SessionProperty.IsInt">
            <summary>
            Signal if this Session Property is a int value
            </summary>
        </member>
        <member name="P:Fusion.SessionProperty.IsString">
            <summary>
            Signal if this Session Property is a string value
            </summary>
        </member>
        <member name="P:Fusion.SessionProperty.Isbool">
            <summary>
            Signal if this Session Property is a bool value
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(Fusion.SessionProperty)~System.Int32">
            <summary>
            Convert a <see cref="T:Fusion.SessionProperty"/> into int
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(System.Int32)~Fusion.SessionProperty">
            <summary>
            Convert a int into a <see cref="T:Fusion.SessionProperty"/>
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(Fusion.SessionProperty)~System.String">
            <summary>
            Convert a <see cref="T:Fusion.SessionProperty"/> into string
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(System.String)~Fusion.SessionProperty">
            <summary>
            Convert a string into a <see cref="T:Fusion.SessionProperty"/>
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(Fusion.SessionProperty)~System.Boolean">
            <summary>
            Convert a <see cref="T:Fusion.SessionProperty"/> into bool
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.op_Implicit(System.Boolean)~Fusion.SessionProperty">
            <summary>
            Convert a bool into a <see cref="T:Fusion.SessionProperty"/>
            </summary>
        </member>
        <member name="M:Fusion.SessionProperty.Support(System.Object)">
            <summary>
            Signal if a particular object is supported as a <see cref="T:Fusion.SessionProperty"/>
            </summary>
            <param name="obj">Object ref to check</param>
            <returns>True if obj is of a supported type, false otherwise</returns>
        </member>
        <member name="M:Fusion.SessionProperty.Convert(System.Object)">
            <summary>
            Convert a particular object into a <see cref="T:Fusion.SessionProperty"/>.
            Check the object type support using <see cref="M:Fusion.SessionProperty.Support(System.Object)"/>.
            If the object type is not supported, <see cref="T:System.ArgumentException"/> will be thrown.
            </summary>
            <param name="obj">Object reference to be converted</param>
            <returns>Instance of a <see cref="T:Fusion.SessionProperty"/> if type is supported, null otherwise</returns>
            <exception cref="T:System.ArgumentException">If the object type passed as argument is not supported</exception>
        </member>
        <member name="M:Fusion.SessionProperty.ToString">
            <summary>
            String representation of the <see cref="T:Fusion.SessionProperty"/>
            </summary>
        </member>
    </members>
</doc>
