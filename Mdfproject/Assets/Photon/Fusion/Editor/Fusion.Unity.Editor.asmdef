{"name": "Fusion.Unity.Editor", "rootNamespace": "Fusion", "references": ["GUID:142cc54f3f73e4dabac1c30b19afb51d", "GUID:19e39f4cea842594788e0c7be4c044c8", "GUID:1baf1ffff1031e948b3b228ab3453251", "GUID:9e24947de15b9834991c9d8411ea37cf", "GUID:69448af7b92c7f342b298e06a37122aa", "GUID:84651a3751eca9349aac36a66bba901b"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": true, "precompiledReferences": ["Mono.Cecil.dll", "Mono.Cecil.Pdb.dll", "Mono.Cecil.Mdb.dll", "Mono.Cecil.Rocks.dll", "Fusion.Common.dll", "Fusion.Runtime.dll", "Fusion.Realtime.dll", "Fusion.Log.dll", "Sirenix.OdinInspector.Editor.dll", "Sirenix.Utilities.Editor.dll", "Sirenix.Utilities.dll", "Sirenix.OdinInspector.Attributes.dll", "Newtonsoft.Json.dll"], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.nuget.mono-cecil", "expression": "1", "define": "FUSION_HAS_MONO_CECIL"}, {"name": "nuget.mono-cecil", "expression": "0.1", "define": "FUSION_HAS_MONO_CECIL"}, {"name": "com.unity.nuget.mono-cecil", "expression": "1.11", "define": "FUSION_CECIL_1_11_OR_NEWER"}, {"name": "com.unity.addressables", "expression": "1.0", "define": "FUSION_ENABLE_ADDRESSABLES"}, {"name": "com.unity.ide.rider", "expression": "1.0", "define": "FUSION_HAS_JETBRAINS_ATTRIBUTES"}, {"name": "com.unity.nuget.newtonsoft-json", "expression": "1.0", "define": "FUSION_HAS_NEWTONSOFT_JSON"}, {"name": "com.unity.multiplayer.playmode", "expression": "0.6", "define": "FUSION_HAS_MPPM"}], "noEngineReferences": false}